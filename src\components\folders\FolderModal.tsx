import React, { useState } from "react";
import {
  ExclamationTriangleIcon,
  XMarkIcon,
  FolderPlusIcon,
} from "@heroicons/react/24/outline";
import { folderService, type Folder } from "../../lib/folderService";
import { useAuth } from "../../contexts/AuthContext";
import toast from "react-hot-toast";

interface FolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  folder: Folder | null;
  action: "delete" | "create" | null;
  onFolderDeleted?: (folderId: string) => void;
  onFolderCreated?: (folder: Folder) => void;
  currentFolderId?: string | null;
}

export function FolderModal({
  isOpen,
  onClose,
  folder,
  action,
  onFolderDeleted,
  onFolderCreated,
  currentFolderId,
}: FolderModalProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [folderName, setFolderName] = useState("");
  const [folderDescription, setFolderDescription] = useState("");

  if (!isOpen || !action) return null;
  if (action === "delete" && !folder) return null;

  const handleDelete = async () => {
    if (!user || !folder) return;

    setIsLoading(true);
    try {
      await folderService.deleteFolder(folder.id, user.id);
      toast.success("Folder deleted successfully");
      onFolderDeleted?.(folder.id);
      onClose();
    } catch (error: any) {
      console.error("Delete folder error:", error);
      toast.error(error.message || "Failed to delete folder");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = async () => {
    if (!user || !folderName.trim()) return;

    setIsLoading(true);
    try {
      const newFolder = await folderService.createFolder(
        {
          name: folderName.trim(),
          description: folderDescription.trim() || undefined,
          parent_folder_id: currentFolderId || undefined,
        },
        user.id
      );
      toast.success("Folder created successfully");
      onFolderCreated?.(newFolder);
      onClose();
    } catch (error: any) {
      console.error("Create folder error:", error);
      toast.error(error.message || "Failed to create folder");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (action === "delete") {
    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        onClick={handleBackdropClick}
      >
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
              <h3 className="text-lg font-medium text-gray-900">
                Delete Folder
              </h3>
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
              disabled={isLoading}
            >
              <XMarkIcon className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          <div className="p-6">
            <p className="text-gray-700 mb-4">
              Are you sure you want to delete the folder{" "}
              <span className="font-medium">"{folder?.name}"</span>?
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">
                    This action cannot be undone.
                  </p>
                  <p>
                    The folder can only be deleted if it's empty (contains no
                    documents or subfolders).
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              onClick={onClose}
              className="btn-secondary"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              disabled={isLoading}
              className="btn-danger flex items-center space-x-2"
            >
              <span>{isLoading ? "Deleting..." : "Delete Folder"}</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (action === "create") {
    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        onClick={handleBackdropClick}
      >
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <FolderPlusIcon className="h-6 w-6 text-green-600" />
              <h3 className="text-lg font-medium text-gray-900">
                Create New Folder
              </h3>
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
              disabled={isLoading}
            >
              <XMarkIcon className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          <div className="p-6 space-y-4">
            <div>
              <label
                htmlFor="folderName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Folder Name *
              </label>
              <input
                id="folderName"
                type="text"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                className="input-field w-full"
                placeholder="Enter folder name"
                disabled={isLoading}
                autoFocus
              />
            </div>
            <div>
              <label
                htmlFor="folderDescription"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Description (Optional)
              </label>
              <textarea
                id="folderDescription"
                value={folderDescription}
                onChange={(e) => setFolderDescription(e.target.value)}
                className="input-field w-full"
                placeholder="Enter folder description"
                rows={3}
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              onClick={onClose}
              className="btn-secondary"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleCreate}
              disabled={isLoading || !folderName.trim()}
              className="btn-primary flex items-center space-x-2"
            >
              <FolderPlusIcon className="h-4 w-4" />
              <span>{isLoading ? "Creating..." : "Create Folder"}</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
