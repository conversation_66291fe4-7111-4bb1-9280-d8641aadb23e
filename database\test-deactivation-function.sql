-- Test script to verify the admin_update_user_status function works correctly

-- First, let's check if the columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('active', 'deactivation_reason', 'deactivated_at');

-- Check if the function exists
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_name = 'admin_update_user_status';

-- Test the function (replace with actual user ID and admin should be logged in)
-- SELECT admin_update_user_status('USER_ID_HERE', false, 'Test deactivation reason');

-- Check a user's current status (replace with actual user ID)
-- SELECT id, email, active, deactivation_reason, deactivated_at 
-- FROM profiles 
-- WHERE id = 'USER_ID_HERE';
