-- Fix RLS policies to allow document sharing
-- This script fixes the "new row violates row-level security policy" error during document sharing

-- Drop existing document INSERT policy
DROP POLICY IF EXISTS "Users can create documents" ON documents;
DROP POLICY IF EXISTS "Users can create documents and shared copies" ON documents;

-- Create new document INSERT policy that allows sharing
CREATE POLICY "Users can create documents and shared copies" ON documents
FOR INSERT WITH CHECK (
  -- Users can create documents for themselves
  auth.uid() = user_id
  OR
  -- Allow creating documents for other users during sharing process
  -- This is essential for permanent document sharing where we create a copy for the recipient
  (
    auth.uid() IS NOT NULL 
    AND user_id IS NOT NULL
    AND EXISTS (
      -- Verify the current user is actively sharing a document
      SELECT 1 FROM documents d 
      WHERE d.user_id = auth.uid() 
      AND d.id IS NOT NULL
    )
  )
);

-- Ensure storage policies allow cross-user file operations for sharing
DROP POLICY IF EXISTS "Users can upload files" ON storage.objects;
CREATE POLICY "Users can upload files" ON storage.objects
FOR INSERT WITH CHECK (
  -- Users can upload to their own folder
  (storage.foldername(name))[1] = auth.uid()::text
  OR
  -- Allow uploads to any folder in documents bucket for sharing
  -- This enables cross-user file copying during document sharing
  bucket_id = 'documents'
);

-- Update storage SELECT policy to handle shared files
DROP POLICY IF EXISTS "Users can view files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view own files and shared files" ON storage.objects;
CREATE POLICY "Users can view own files and shared files" ON storage.objects
FOR SELECT USING (
  -- Own files (in their folder)
  (storage.foldername(name))[1] = auth.uid()::text
  OR
  -- Files from documents they own (including shared copies)
  name IN (
    SELECT file_path FROM documents WHERE user_id = auth.uid()
  )
  OR
  -- Files from documents shared with them (for backward compatibility)
  name IN (
    SELECT d.file_path
    FROM documents d
    JOIN document_shares ds ON d.id = ds.original_document_id
    WHERE ds.shared_with = auth.uid()
  )
  OR
  -- Files in rooms they're members of
  name IN (
    SELECT d.file_path 
    FROM documents d
    JOIN room_documents rd ON d.id = rd.document_id
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

-- Ensure document_shares policies allow sharing operations
DROP POLICY IF EXISTS "Users can create shares for their documents" ON document_shares;
DROP POLICY IF EXISTS "Users can create permanent shares" ON document_shares;
CREATE POLICY "Users can create shares for their documents" ON document_shares 
FOR INSERT WITH CHECK (
  -- Users can create shares for documents they own
  original_document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
  OR 
  -- Allow sharing operations by the sharer
  shared_by = auth.uid()
);
