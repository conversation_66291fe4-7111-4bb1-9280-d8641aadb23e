import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAuth } from "../../contexts/AuthContext";
import { cn } from "../../lib/utils";
import { usePageTitle } from "../../hooks/usePageTitle";
import { AccountDeactivated } from "./AccountDeactivated";
import { LoadingButton } from "../ui/LoadingSpinner";

const _loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof _loginSchema>;

export function LoginForm() {
  const { signIn, actionLoading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [emailNotRegistered, setEmailNotRegistered] = useState(false);
  const [deactivatedAccount, setDeactivatedAccount] = useState<{
    email: string;
    reason: string;
    deactivatedAt: string;
  } | null>(null);

  // Set page title
  usePageTitle("Login");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>();

  const onSubmit = async (data: LoginFormData) => {
    try {
      // Reset email not registered state
      setEmailNotRegistered(false);
      await signIn(data.email, data.password);
    } catch (error: any) {
      // Handle deactivated account specifically
      if (error?.code === "ACCOUNT_DEACTIVATED" && error?.details) {
        setDeactivatedAccount({
          email: error.details.email,
          reason: error.details.reason,
          deactivatedAt: error.details.deactivatedAt,
        });
        return;
      }

      const errorMessage = error?.message || "An error occurred during login";

      if (errorMessage.includes("not registered")) {
        // Email not found - show error on email field and set flag
        setError("email", { message: errorMessage });
        setEmailNotRegistered(true);
      } else if (errorMessage.includes("Incorrect password")) {
        // Wrong password - show error on password field
        setError("password", { message: errorMessage });
        setEmailNotRegistered(false);
      } else if (errorMessage.includes("Invalid login credentials")) {
        // Fallback for generic auth errors
        setError("email", { message: "Invalid email or password" });
        setError("password", { message: "Invalid email or password" });
        setEmailNotRegistered(false);
      } else {
        // Other errors - show on email field
        setError("email", { message: errorMessage });
        setEmailNotRegistered(false);
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-theme duration-theme">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center transition-theme duration-theme">
            <span className="text-2xl">🎒</span>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
            Welcome back to BrimBag
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Sign in to access your digital documents
          </p>
        </div>

        <div className="card p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Email address
              </label>
              <input
                {...register("email")}
                type="email"
                autoComplete="email"
                disabled={actionLoading}
                className={cn(
                  "input-field",
                  errors.email && "border-red-300 focus:ring-red-500",
                  actionLoading && "opacity-60 cursor-not-allowed"
                )}
                placeholder="Enter your email"
                onChange={(e) => {
                  // Reset error states when user starts typing
                  if (emailNotRegistered) {
                    setEmailNotRegistered(false);
                  }
                  // Call the original onChange from react-hook-form
                  register("email").onChange(e);
                }}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.email.message}
                </p>
              )}
              {emailNotRegistered && (
                <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg transition-theme duration-theme">
                  <p className="text-sm text-blue-700 dark:text-blue-300 transition-theme duration-theme">
                    Don't have an account yet?{" "}
                    <Link
                      to="/register"
                      className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-200"
                    >
                      Sign up here
                    </Link>
                  </p>
                </div>
              )}
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Password
              </label>
              <div className="relative">
                <input
                  {...register("password")}
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  disabled={actionLoading}
                  className={cn(
                    "input-field pr-10",
                    errors.password && "border-red-300 focus:ring-red-500",
                    actionLoading && "opacity-60 cursor-not-allowed"
                  )}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-dark-600 dark:bg-dark-700 dark:text-primary-400 dark:focus:ring-primary-400 transition-theme duration-theme"
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-sm text-gray-700 dark:text-gray-300 transition-theme duration-theme"
                >
                  Remember me
                </label>
              </div>

              <Link
                to="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-theme duration-theme"
              >
                Forgot your password?
              </Link>
            </div>

            <LoadingButton
              type="submit"
              loading={actionLoading}
              loadingText="Signing in..."
              variant="primary"
              className="w-full"
            >
              Sign in
            </LoadingButton>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-dark-900 text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                  New to SmartBagPack?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                to="/register"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-dark-600 rounded-lg shadow-sm bg-white dark:bg-dark-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-primary-400 dark:focus:ring-offset-dark-800 transition-theme duration-theme"
              >
                Create your account
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Account Deactivated Modal */}
      {deactivatedAccount && (
        <AccountDeactivated
          email={deactivatedAccount.email}
          reason={deactivatedAccount.reason}
          deactivatedAt={deactivatedAccount.deactivatedAt}
          onClose={() => setDeactivatedAccount(null)}
        />
      )}
    </div>
  );
}
