-- Improved User Deletion Process
-- This enhances the user deletion to be more robust and prevent orphaned users

-- ============================================================================
-- PART 1: Enhanced User Deletion Function
-- ============================================================================

-- Drop existing function
DROP FUNCTION IF EXISTS admin_delete_user(UUID);

-- Create improved user deletion function with better error handling
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  admin_actions INTEGER := 0;
  shared_documents_nullified INTEGER := 0;
BEGIN
  -- Check if current user is admin OR deleting their own account
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') 
     AND auth.uid() != p_user_id THEN
    RAISE EXCEPTION 'Access denied: Admin role required or can only delete own account';
  END IF;
  
  -- Allow self-deletion for regular users, but prevent admin self-deletion
  IF auth.uid() = p_user_id AND EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Admin users cannot delete their own account';
  END IF;
  
  -- Get user info for logging (from profiles if exists, otherwise from auth.users)
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;
  
  -- If no profile, get email from auth.users
  IF user_email IS NULL THEN
    SELECT email INTO user_email FROM auth.users WHERE id = p_user_id;
    user_name := 'Unknown User';
  END IF;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found in profiles or auth.users';
  END IF;
  
  -- Count records before deletion for logging
  SELECT COUNT(*) INTO documents FROM documents WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO folders FROM folders WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO room_memberships FROM room_members WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO rooms_created FROM rooms WHERE created_by = p_user_id;
  SELECT COUNT(*) INTO document_shares FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  SELECT COUNT(*) INTO activity_logs FROM activity_logs WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO admin_actions FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Count shared documents that will be nullified
  SELECT COUNT(*) INTO shared_documents_nullified FROM documents WHERE shared_from_user_id = p_user_id;
  
  -- Build deletion summary
  deleted_counts := json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'admin_actions_removed', admin_actions,
    'shared_documents_nullified', shared_documents_nullified
  );
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete admin actions
  DELETE FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;
  
  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications
  DELETE FROM notifications WHERE user_id = p_user_id;
  
  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;
  
  -- Delete room invitations
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;
  
  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile (if it exists)
  DELETE FROM profiles WHERE id = p_user_id;
  
  -- Note: Auth user deletion is handled by client-side admin API
  -- This ensures proper cleanup of the authentication record
  
  -- Log the admin action (only if not self-deletion)
  IF auth.uid() != p_user_id THEN
    -- Use a separate transaction for logging to avoid issues if logging fails
    BEGIN
      INSERT INTO activity_logs (user_id, action_type, target_type, target_id, details)
      VALUES (
        auth.uid(),
        'user_delete',
        'user', 
        p_user_id,
        deleted_counts::jsonb
      );
    EXCEPTION
      WHEN OTHERS THEN
        -- Log failure but don't fail the deletion
        NULL;
    END;
  END IF;
  
  RETURN deleted_counts;
EXCEPTION
  WHEN OTHERS THEN
    -- Return error information
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'user_id', p_user_id,
      'user_email', user_email
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- PART 2: Function to Detect and Report Orphaned Users
-- ============================================================================

-- Function to detect orphaned users (for monitoring)
CREATE OR REPLACE FUNCTION detect_orphaned_users()
RETURNS JSON AS $$
DECLARE
  orphaned_users JSON;
  orphaned_count INTEGER;
BEGIN
  -- Get list of orphaned users (auth.users without profiles)
  SELECT json_agg(
    json_build_object(
      'user_id', au.id,
      'email', au.email,
      'created_at', au.created_at,
      'last_sign_in_at', au.last_sign_in_at
    )
  ), COUNT(*)
  INTO orphaned_users, orphaned_count
  FROM auth.users au
  WHERE NOT EXISTS (SELECT 1 FROM profiles p WHERE p.id = au.id)
  AND au.email != '<EMAIL>'; -- Protect admin user
  
  RETURN json_build_object(
    'orphaned_count', COALESCE(orphaned_count, 0),
    'orphaned_users', COALESCE(orphaned_users, '[]'::json),
    'timestamp', NOW(),
    'message', CASE 
      WHEN orphaned_count > 0 THEN 'Orphaned users detected. Use admin API to clean up.'
      ELSE 'No orphaned users found.'
    END
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION detect_orphaned_users() TO authenticated;
