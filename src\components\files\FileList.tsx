import { useState } from "react";
import { fileService } from "../../lib/fileService";
import type { DocumentFile } from "../../lib/fileService";
import { formatFileSize, formatDateTime } from "../../lib/utils";
import { useAuth } from "../../contexts/AuthContext";
import { useDragAndDrop } from "../../hooks/useDragAndDrop";
import toast from "react-hot-toast";
import { DocumentPreview } from "./DocumentPreview";
import { DeleteConfirmDialog } from "./DeleteConfirmDialog";
import { FileIcon } from "../ui/FileIcon";
import {
  ContextMenu,
  useContextMenu,
  createDocumentContextMenuItems,
} from "../ui/ContextMenu";
import { FolderOperationsModal } from "../folders/FolderOperationsModal";
import {
  ThreeDotMenu,
  useIsMobile,
  useTouchHandlers,
} from "../ui/ThreeDotMenu";

// Extended document type with sharing information
export interface DocumentWithSharingInfo extends DocumentFile {
  sharing_info?: {
    is_shared_with_me?: boolean;
    shared_by_user?: string;
    shared_by_name?: string;
    permission?: "view" | "download";
    share_type?: "user" | "room";
  };
}

interface FileListProps {
  documents: (DocumentFile | DocumentWithSharingInfo)[];
  onDocumentUpdate?: (document: DocumentFile) => void;
  onDocumentDelete?: (documentIds: string[]) => void;
  onDocumentShare?: (document: DocumentFile) => void;
  onBulkShareToRoom?: (documents: DocumentFile[]) => void;
  onQuickShareToRoom?: (document: DocumentFile) => void;
  onRefresh?: () => void;

  quickShareRoomId?: string | null;
  viewMode?: "grid" | "list";
  showActions?: boolean;
  enableDragAndDrop?: boolean;
}

export function FileList({
  documents,
  onDocumentUpdate,
  onDocumentDelete,
  onDocumentShare,
  onBulkShareToRoom,
  onQuickShareToRoom,
  onRefresh,
  quickShareRoomId,
  viewMode = "list",
  showActions = true,
  enableDragAndDrop = false,
}: FileListProps) {
  const { user } = useAuth();
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(
    new Set()
  );
  const [editingDocument, setEditingDocument] = useState<string | null>(null);
  const [editForm, setEditForm] = useState({
    title: "",
    description: "",
    is_public: false,
  });
  const [previewDocument, setPreviewDocument] = useState<DocumentFile | null>(
    null
  );
  const [deleteDocuments, setDeleteDocuments] = useState<DocumentFile[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);
  const [contextMenuItems, setContextMenuItems] = useState<any[]>([]);
  const [documentOperationsModal, setDocumentOperationsModal] = useState<{
    isOpen: boolean;
    operation: "copy" | "move" | null;
    document: DocumentFile | null;
  }>({
    isOpen: false,
    operation: null,
    document: null,
  });
  const contextMenu = useContextMenu();
  const isMobile = useIsMobile();
  const touchHandlers = useTouchHandlers();

  // Drag and drop functionality
  const { createDragHandlers } = useDragAndDrop();

  // Helper function to render sharing indicators
  const renderSharingIndicator = (
    document: DocumentFile | DocumentWithSharingInfo
  ) => {
    const docWithSharing = document as DocumentWithSharingInfo;
    const sharingInfo = docWithSharing.sharing_info;

    if (!sharingInfo?.is_shared_with_me) return null;

    const isUserShare = sharingInfo.share_type === "user";
    const isViewOnly = sharingInfo.permission === "view";

    return (
      <div className="flex items-center space-x-1">
        {/* Share type indicator */}
        <div
          className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            isUserShare
              ? "bg-blue-100 text-blue-800"
              : "bg-green-100 text-green-800"
          }`}
          title={
            isUserShare
              ? `Shared by ${sharingInfo.shared_by_name || "user"}`
              : "Shared via room"
          }
        >
          {isUserShare ? (
            <svg
              className="w-3 h-3 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          ) : (
            <svg
              className="w-3 h-3 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          )}
          {isUserShare ? sharingInfo.shared_by_name || "User" : "Room"}
        </div>

        {/* Permission indicator */}
        {isViewOnly && (
          <div
            className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
            title="View only access"
          >
            <svg
              className="w-3 h-3 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            View Only
          </div>
        )}
      </div>
    );
  };

  // Helper function to check if user owns the document
  const isOwnedByUser = (
    document: DocumentFile | DocumentWithSharingInfo
  ): boolean => {
    return user?.id === document.user_id;
  };

  const handleSelectDocument = (documentId: string) => {
    const newSelected = new Set(selectedDocuments);
    if (newSelected.has(documentId)) {
      newSelected.delete(documentId);
    } else {
      newSelected.add(documentId);
    }
    setSelectedDocuments(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedDocuments.size === documents.length) {
      setSelectedDocuments(new Set());
    } else {
      setSelectedDocuments(new Set(documents.map((d) => d.id)));
    }
  };

  const handleDownload = async (document: DocumentFile) => {
    try {
      await fileService.downloadDocument(document);
      toast.success("Download started!");

      // Update the document in the list with new download count
      const updatedDocument = {
        ...document,
        download_count: document.download_count + 1,
      };
      onDocumentUpdate?.(updatedDocument);
    } catch (error: any) {
      toast.error(error.message || "Failed to download file");
    }
  };

  const handleBulkDelete = () => {
    const docsToDelete = documents.filter((doc) =>
      selectedDocuments.has(doc.id)
    );
    setDeleteDocuments(docsToDelete);
  };

  const handleBulkShareToRoom = () => {
    const docsToShare = documents.filter((doc) =>
      selectedDocuments.has(doc.id)
    ) as DocumentFile[];
    onBulkShareToRoom?.(docsToShare);
  };

  const confirmDelete = async () => {
    if (!user) return;

    try {
      setIsDeleting(true);

      if (deleteDocuments.length === 1) {
        await fileService.deleteDocument(deleteDocuments[0].id, user.id);
        onDocumentDelete?.([deleteDocuments[0].id]);
        toast.success("Document deleted successfully");
      } else {
        const result = await fileService.bulkDeleteDocuments(
          deleteDocuments.map((doc) => doc.id),
          user.id
        );

        if (result.success.length > 0) {
          onDocumentDelete?.(result.success);
          toast.success(
            `${result.success.length} document(s) deleted successfully`
          );
        }

        if (result.failed.length > 0) {
          toast.error(`Failed to delete ${result.failed.length} document(s)`);
        }
      }

      setSelectedDocuments(new Set());
    } catch (error: any) {
      toast.error(error.message || "Failed to delete document(s)");
    } finally {
      setIsDeleting(false);
      setDeleteDocuments([]);
    }
  };

  const startEdit = (document: DocumentFile) => {
    setEditingDocument(document.id);
    setEditForm({
      title: document.title,
      description: document.description || "",
      is_public: document.is_public,
    });
  };

  const saveEdit = async (document: DocumentFile) => {
    try {
      const updatedDocument = await fileService.updateDocument(
        document.id,
        {
          title: editForm.title,
          description: editForm.description || undefined,
          is_public: editForm.is_public,
        },
        user!.id
      );
      onDocumentUpdate?.(updatedDocument);
      setEditingDocument(null);
      toast.success("Document updated successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to update document");
    }
  };

  const cancelEdit = () => {
    setEditingDocument(null);
    setEditForm({ title: "", description: "", is_public: false });
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📁</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
          No documents found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
          Upload your first document to get started.
        </p>
      </div>
    );
  }

  if (viewMode === "grid") {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {documents.map((document) => {
          const dragHandlers = enableDragAndDrop
            ? createDragHandlers({
                type: "document",
                id: document.id,
                data: document,
              })
            : {};

          const handleContextMenu = (e: React.MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();

            const items = createDocumentContextMenuItems(document, {
              onPreview: () => setPreviewDocument(document),
              onDownload: () => handleDownload(document),
              onRename: () => startEdit(document),
              onDelete: () => setDeleteDocuments([document]),
              onCopy: () => {
                setDocumentOperationsModal({
                  isOpen: true,
                  operation: "copy",
                  document: document,
                });
              },
              onMove: () => {
                setDocumentOperationsModal({
                  isOpen: true,
                  operation: "move",
                  document: document,
                });
              },
              onShare: onDocumentShare
                ? () => onDocumentShare(document)
                : undefined,
            });

            setContextMenuItems(items);
            contextMenu.openContextMenu(e);
          };

          return (
            <div
              key={document.id}
              className={`card p-4 hover:shadow-md transition-shadow duration-200 ${
                enableDragAndDrop ? "cursor-move" : ""
              }`}
              onContextMenu={isMobile ? undefined : handleContextMenu}
              {...(isMobile ? touchHandlers.touchHandlers : {})}
              {...dragHandlers}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center justify-center w-12 h-12 bg-gray-100 dark:bg-dark-700 rounded-lg transition-theme duration-theme">
                  <FileIcon
                    fileType={document.file_type}
                    mimeType={document.mime_type}
                    size="lg"
                  />
                </div>
                {showActions && (
                  <div className="flex items-center space-x-1">
                    <ThreeDotMenu
                      title={document.title}
                      items={createDocumentContextMenuItems(document, {
                        onPreview: () => setPreviewDocument(document),
                        onDownload: () => handleDownload(document),
                        onRename: () => startEdit(document),
                        onDelete: () => setDeleteDocuments([document]),
                        onCopy: () => {
                          setDocumentOperationsModal({
                            isOpen: true,
                            operation: "copy",
                            document: document,
                          });
                        },
                        onMove: () => {
                          setDocumentOperationsModal({
                            isOpen: true,
                            operation: "move",
                            document: document,
                          });
                        },
                        onShare: onDocumentShare
                          ? () => onDocumentShare(document)
                          : undefined,
                      })}
                      size="sm"
                    />
                    <input
                      type="checkbox"
                      checked={selectedDocuments.has(document.id)}
                      onChange={() => handleSelectDocument(document.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <h4
                  className="font-medium text-gray-900 dark:text-gray-100 truncate cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-theme duration-theme"
                  title={document.title}
                  onClick={() => setPreviewDocument(document)}
                >
                  {document.title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 transition-theme duration-theme">
                  {document.description || "No description"}
                </p>

                {/* Sharing indicators */}
                <div className="mb-2">{renderSharingIndicator(document)}</div>

                <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1 transition-theme duration-theme">
                  <p>{formatFileSize(document.file_size)}</p>
                  <p>{formatDateTime(document.created_at)}</p>
                  <p>{document.download_count} downloads</p>
                </div>
              </div>

              {showActions && (
                <div className="mt-4 flex items-center justify-between">
                  {/* Quick share to room button when room is pre-selected */}
                  {quickShareRoomId && isOwnedByUser(document) && (
                    <button
                      onClick={() => onQuickShareToRoom?.(document)}
                      className="text-blue-600 hover:text-blue-800 text-sm p-2 rounded hover:bg-blue-50 min-h-[44px] min-w-[44px] flex items-center justify-center"
                      title="Share to Room"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 715.196-2.121M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </button>
                  )}

                  {/* Three-dot menu for all actions */}
                  <ThreeDotMenu
                    title={document.title}
                    items={createDocumentContextMenuItems(document, {
                      onPreview: () => setPreviewDocument(document),
                      onDownload: () => handleDownload(document),
                      onRename: () => startEdit(document),
                      onDelete: () => setDeleteDocuments([document]),
                      onCopy: () => {
                        setDocumentOperationsModal({
                          isOpen: true,
                          operation: "copy",
                          document: document,
                        });
                      },
                      onMove: () => {
                        setDocumentOperationsModal({
                          isOpen: true,
                          operation: "move",
                          document: document,
                        });
                      },
                      onShare: onDocumentShare
                        ? () => onDocumentShare(document)
                        : undefined,
                    })}
                    size="md"
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {showActions && selectedDocuments.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <span className="text-sm text-blue-800 font-medium">
              {selectedDocuments.size} document(s) selected
            </span>
            <div className="flex items-center space-x-2">
              {onBulkShareToRoom && (
                <button
                  onClick={handleBulkShareToRoom}
                  className="text-sm bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 flex items-center space-x-1"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                    />
                  </svg>
                  <span>Share to Room</span>
                </button>
              )}
              <button
                onClick={handleBulkDelete}
                className="text-sm bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 flex items-center space-x-1"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
                <span>Delete Selected</span>
              </button>
              <button
                onClick={() => setSelectedDocuments(new Set())}
                className="text-sm text-blue-600 hover:text-blue-800 px-3 py-2 rounded hover:bg-blue-100"
              >
                Clear selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table Header - Hidden on mobile */}
      <div className="card hidden md:block">
        <div className="px-6 py-3 border-b border-gray-200">
          <div className="flex items-center">
            {showActions && (
              <div className="flex items-center mr-4">
                <input
                  type="checkbox"
                  checked={
                    selectedDocuments.size === documents.length &&
                    documents.length > 0
                  }
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            )}
            <div className="grid grid-cols-12 gap-4 w-full text-sm font-medium text-gray-700">
              <div className="col-span-4">Name</div>
              <div className="col-span-2">Type</div>
              <div className="col-span-2">Size</div>
              <div className="col-span-2">Modified</div>
              <div className="col-span-1">Downloads</div>
              {showActions && <div className="col-span-1">Actions</div>}
            </div>
          </div>
        </div>

        {/* Desktop Table Body */}
        <div className="divide-y divide-gray-200 hidden md:block">
          {documents.map((document) => {
            const dragHandlers = enableDragAndDrop
              ? createDragHandlers({
                  type: "document",
                  id: document.id,
                  data: document,
                })
              : {};

            return (
              <div
                key={document.id}
                className={`px-6 py-4 hover:bg-gray-50 ${
                  enableDragAndDrop ? "cursor-move" : ""
                }`}
                {...dragHandlers}
              >
                {editingDocument === document.id ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="text"
                        value={editForm.title}
                        onChange={(e) =>
                          setEditForm((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        className="input-field"
                        placeholder="Document title"
                      />
                      <input
                        type="text"
                        value={editForm.description}
                        onChange={(e) =>
                          setEditForm((prev) => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                        className="input-field"
                        placeholder="Description"
                      />
                    </div>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editForm.is_public}
                          onChange={(e) =>
                            setEditForm((prev) => ({
                              ...prev,
                              is_public: e.target.checked,
                            }))
                          }
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Public
                        </span>
                      </label>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => saveEdit(document)}
                          className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                        >
                          Save
                        </button>
                        <button
                          onClick={cancelEdit}
                          className="text-sm bg-gray-300 text-gray-700 px-3 py-1 rounded hover:bg-gray-400"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center">
                    {showActions && (
                      <div className="flex items-center mr-4">
                        <input
                          type="checkbox"
                          checked={selectedDocuments.has(document.id)}
                          onChange={() => handleSelectDocument(document.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                    )}
                    <div className="grid grid-cols-12 gap-4 w-full items-center">
                      <div className="col-span-4 flex items-center space-x-3">
                        <span className="text-2xl">
                          {fileService.getFileIcon(document.file_type)}
                        </span>
                        <div>
                          <p
                            className="font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-theme duration-theme"
                            onClick={() => setPreviewDocument(document)}
                          >
                            {document.title}
                          </p>
                          {document.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 truncate transition-theme duration-theme">
                              {document.description}
                            </p>
                          )}
                          <div className="flex items-center space-x-2 mt-1">
                            {document.is_public && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                Public
                              </span>
                            )}
                            {renderSharingIndicator(document)}
                          </div>
                        </div>
                      </div>
                      <div className="col-span-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        {document.file_type}
                      </div>
                      <div className="col-span-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        {formatFileSize(document.file_size)}
                      </div>
                      <div className="col-span-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        {formatDateTime(document.created_at)}
                      </div>
                      <div className="col-span-1 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        {document.download_count}
                      </div>
                      {showActions && (
                        <div className="col-span-1 flex items-center justify-end space-x-2">
                          {/* Three-dot menu for all actions */}
                          <ThreeDotMenu
                            title={document.title}
                            items={createDocumentContextMenuItems(document, {
                              onPreview: () => setPreviewDocument(document),
                              onDownload: () => handleDownload(document),
                              onRename: () => startEdit(document),
                              onDelete: () => setDeleteDocuments([document]),
                              onCopy: () => {
                                setDocumentOperationsModal({
                                  isOpen: true,
                                  operation: "copy",
                                  document: document,
                                });
                              },
                              onMove: () => {
                                setDocumentOperationsModal({
                                  isOpen: true,
                                  operation: "move",
                                  document: document,
                                });
                              },
                              onShare: onDocumentShare
                                ? () => onDocumentShare(document)
                                : undefined,
                            })}
                            size="md"
                          />

                          {/* Quick share to room button when room is pre-selected */}
                          {quickShareRoomId && isOwnedByUser(document) && (
                            <button
                              onClick={() => onQuickShareToRoom?.(document)}
                              className="text-blue-600 hover:text-blue-800"
                              title="Share to Room"
                            >
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Mobile Select All - Only show when there are documents and actions are enabled */}
      {showActions && documents.length > 0 && (
        <div className="md:hidden">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={
                  selectedDocuments.size === documents.length &&
                  documents.length > 0
                }
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">
                Select all ({documents.length})
              </span>
            </label>
            {selectedDocuments.size > 0 && (
              <span className="text-sm text-blue-600 font-medium">
                {selectedDocuments.size} selected
              </span>
            )}
          </div>
        </div>
      )}

      {/* Mobile Card Layout */}
      <div className="md:hidden space-y-4">
        {documents.map((document) => {
          const dragHandlers = enableDragAndDrop
            ? createDragHandlers({
                type: "document",
                id: document.id,
                data: document,
              })
            : {};

          return (
            <div
              key={document.id}
              className={`card p-4 ${enableDragAndDrop ? "cursor-move" : ""}`}
              {...(isMobile ? touchHandlers.touchHandlers : {})}
              {...dragHandlers}
            >
              {editingDocument === document.id ? (
                <div className="space-y-4">
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={editForm.title}
                      onChange={(e) =>
                        setEditForm((prev) => ({
                          ...prev,
                          title: e.target.value,
                        }))
                      }
                      className="input-field w-full"
                      placeholder="Document title"
                    />
                    <input
                      type="text"
                      value={editForm.description}
                      onChange={(e) =>
                        setEditForm((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      className="input-field w-full"
                      placeholder="Description"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editForm.is_public}
                        onChange={(e) =>
                          setEditForm((prev) => ({
                            ...prev,
                            is_public: e.target.checked,
                          }))
                        }
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Public</span>
                    </label>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => saveEdit(document)}
                        className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                      >
                        Save
                      </button>
                      <button
                        onClick={cancelEdit}
                        className="text-sm bg-gray-300 text-gray-700 px-3 py-1 rounded hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Header with checkbox and title */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1 min-w-0">
                      {showActions && !isMobile && (
                        <input
                          type="checkbox"
                          checked={selectedDocuments.has(document.id)}
                          onChange={() => handleSelectDocument(document.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                        />
                      )}
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <span className="text-2xl flex-shrink-0">
                          {fileService.getFileIcon(document.file_type)}
                        </span>
                        <div className="flex-1 min-w-0">
                          <p
                            className="font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 truncate transition-theme duration-theme"
                            onClick={() => setPreviewDocument(document)}
                          >
                            {document.title}
                          </p>
                          {document.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1 transition-theme duration-theme">
                              {document.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Three-dot menu for mobile */}
                    {showActions && isMobile && (
                      <ThreeDotMenu
                        title={document.title}
                        items={createDocumentContextMenuItems(document, {
                          onPreview: () => setPreviewDocument(document),
                          onDownload: () => handleDownload(document),
                          onRename: () => startEdit(document),
                          onDelete: () => setDeleteDocuments([document]),
                          onCopy: () => {
                            setDocumentOperationsModal({
                              isOpen: true,
                              operation: "copy",
                              document: document,
                            });
                          },
                          onMove: () => {
                            setDocumentOperationsModal({
                              isOpen: true,
                              operation: "move",
                              document: document,
                            });
                          },
                          onShare: onDocumentShare
                            ? () => onDocumentShare(document)
                            : undefined,
                        })}
                        size="sm"
                      />
                    )}
                  </div>

                  {/* Document metadata */}
                  <div className="grid grid-cols-2 gap-3 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    <div>
                      <span className="font-medium">Type:</span>{" "}
                      {document.file_type}
                    </div>
                    <div>
                      <span className="font-medium">Size:</span>{" "}
                      {formatFileSize(document.file_size)}
                    </div>
                    <div>
                      <span className="font-medium">Modified:</span>{" "}
                      {formatDateTime(document.created_at)}
                    </div>
                    <div>
                      <span className="font-medium">Downloads:</span>{" "}
                      {document.download_count}
                    </div>
                  </div>

                  {/* Status indicators */}
                  <div className="flex items-center space-x-2 flex-wrap">
                    {document.is_public && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        Public
                      </span>
                    )}
                    {renderSharingIndicator(document)}
                  </div>

                  {/* Three-dot menu for desktop only - mobile has it in the header */}
                  {showActions && !isMobile && (
                    <div className="flex items-center justify-end pt-2 border-t border-gray-200">
                      <ThreeDotMenu
                        title={document.title}
                        items={createDocumentContextMenuItems(document, {
                          onPreview: () => setPreviewDocument(document),
                          onDownload: () => handleDownload(document),
                          onRename: () => startEdit(document),
                          onDelete: () => setDeleteDocuments([document]),
                          onCopy: () => {
                            setDocumentOperationsModal({
                              isOpen: true,
                              operation: "copy",
                              document: document,
                            });
                          },
                          onMove: () => {
                            setDocumentOperationsModal({
                              isOpen: true,
                              operation: "move",
                              document: document,
                            });
                          },
                          onShare: onDocumentShare
                            ? () => onDocumentShare(document)
                            : undefined,
                        })}
                        size="md"
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Preview Modal */}
      {previewDocument && (
        <DocumentPreview
          document={previewDocument}
          onClose={() => setPreviewDocument(null)}
          onDownload={() => {
            const updatedDocument = {
              ...previewDocument,
              download_count: previewDocument.download_count + 1,
            };
            onDocumentUpdate?.(updatedDocument);
            setPreviewDocument(null);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {deleteDocuments.length > 0 && (
        <DeleteConfirmDialog
          documents={deleteDocuments}
          onConfirm={confirmDelete}
          onCancel={() => setDeleteDocuments([])}
          isDeleting={isDeleting}
        />
      )}

      {/* Context Menu */}
      <ContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        items={contextMenuItems}
        onClose={contextMenu.closeContextMenu}
      />

      {/* Document Operations Modal */}
      <FolderOperationsModal
        isOpen={documentOperationsModal.isOpen}
        onClose={() =>
          setDocumentOperationsModal({
            isOpen: false,
            operation: null,
            document: null,
          })
        }
        operation={documentOperationsModal.operation}
        sourceDocument={documentOperationsModal.document || undefined}
        onSuccess={() => {
          // Refresh the document list
          onRefresh?.();
        }}
      />
    </div>
  );
}
