import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { XMarkIcon, StarIcon } from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import toast from "react-hot-toast";
import { feedbackService } from "../../lib/feedbackService";
import type { FeedbackCategory } from "../../lib/feedbackService";

const feedbackSchema = z.object({
  feedbackText: z
    .string()
    .min(10, "Feedback must be at least 10 characters")
    .max(1000, "Feedback must be less than 1000 characters"),
  rating: z
    .number()
    .min(1, "Please select a rating")
    .max(5, "Rating must be between 1 and 5"),
  category: z.enum(["bug_report", "feature_request", "general", "ui_ux"], {
    required_error: "Please select a category",
  }),
});

type FeedbackFormData = z.infer<typeof feedbackSchema>;

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onFeedbackSubmitted?: () => void;
}

const categoryOptions = [
  {
    value: "bug_report" as FeedbackCategory,
    label: "Bug Report",
    description: "Report a bug or issue",
  },
  {
    value: "feature_request" as FeedbackCategory,
    label: "Feature Request",
    description: "Suggest a new feature",
  },
  {
    value: "general" as FeedbackCategory,
    label: "General Feedback",
    description: "General comments or suggestions",
  },
  {
    value: "ui_ux" as FeedbackCategory,
    label: "UI/UX Issue",
    description: "User interface or experience feedback",
  },
];

export function FeedbackModal({
  isOpen,
  onClose,
  userId,
  onFeedbackSubmitted,
}: FeedbackModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [hoveredRating, setHoveredRating] = useState<number>(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<FeedbackFormData>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      feedbackText: "",
      rating: 0,
      category: undefined,
    },
  });

  const watchedText = watch("feedbackText", "");
  const remainingChars = 1000 - watchedText.length;

  const handleRatingClick = (rating: number) => {
    setSelectedRating(rating);
    setValue("rating", rating, { shouldValidate: true });
  };

  const onSubmit = async (data: FeedbackFormData) => {
    try {
      setIsSubmitting(true);

      await feedbackService.submitFeedback({
        userId,
        feedbackText: data.feedbackText,
        rating: data.rating,
        category: data.category,
      });

      toast.success("Thank you for your feedback! We appreciate your input.");
      reset();
      setSelectedRating(0);
      onFeedbackSubmitted?.();
      onClose();
    } catch (error: any) {
      console.error("Failed to submit feedback:", error);
      toast.error(
        error.message || "Failed to submit feedback. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      setSelectedRating(0);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white dark:bg-dark-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transition-theme duration-theme">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">💬</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  Send Feedback
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Help us improve BrimBag
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors disabled:opacity-50"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                How would you rate your experience? *
              </label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => {
                  const isFilled = star <= (hoveredRating || selectedRating);
                  const StarComponent = isFilled ? StarIconSolid : StarIcon;

                  return (
                    <button
                      key={star}
                      type="button"
                      onClick={() => handleRatingClick(star)}
                      onMouseEnter={() => setHoveredRating(star)}
                      onMouseLeave={() => setHoveredRating(0)}
                      className={`p-1 transition-colors ${
                        isFilled
                          ? "text-yellow-400"
                          : "text-gray-300 dark:text-gray-600 hover:text-yellow-400"
                      }`}
                    >
                      <StarComponent className="w-8 h-8" />
                    </button>
                  );
                })}
                <span className="ml-3 text-sm text-gray-600 dark:text-gray-400">
                  {selectedRating > 0 && (
                    <>
                      {selectedRating} star{selectedRating !== 1 ? "s" : ""}
                      {selectedRating === 1 && " - Poor"}
                      {selectedRating === 2 && " - Fair"}
                      {selectedRating === 3 && " - Good"}
                      {selectedRating === 4 && " - Very Good"}
                      {selectedRating === 5 && " - Excellent"}
                    </>
                  )}
                </span>
              </div>
              {errors.rating && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.rating.message}
                </p>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Category *
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {categoryOptions.map((option) => {
                  const isSelected = watch("category") === option.value;
                  return (
                    <label
                      key={option.value}
                      className={`relative flex items-start p-4 border rounded-lg cursor-pointer transition-colors ${
                        isSelected
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400"
                          : "border-gray-200 dark:border-dark-600 hover:bg-gray-50 dark:hover:bg-dark-700"
                      }`}
                    >
                      <input
                        type="radio"
                        {...register("category")}
                        value={option.value}
                        className="sr-only"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {option.label}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {option.description}
                        </div>
                      </div>
                      <div className="ml-3 flex items-center h-5">
                        <div
                          className={`w-4 h-4 border-2 rounded-full flex items-center justify-center transition-colors ${
                            isSelected
                              ? "border-blue-500 bg-blue-500"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                        >
                          {isSelected && (
                            <div className="w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                      </div>
                    </label>
                  );
                })}
              </div>
              {errors.category && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.category.message}
                </p>
              )}
            </div>

            {/* Feedback Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Your Feedback *
              </label>
              <textarea
                {...register("feedbackText")}
                rows={6}
                placeholder="Please share your thoughts, suggestions, or report any issues you've encountered..."
                className="w-full px-4 py-3 border border-gray-200 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-theme duration-theme resize-none"
              />
              <div className="flex justify-between items-center mt-2">
                <div>
                  {errors.feedbackText && (
                    <p className="text-sm text-red-600 dark:text-red-400">
                      {errors.feedbackText.message}
                    </p>
                  )}
                </div>
                <p
                  className={`text-xs ${
                    remainingChars < 100
                      ? "text-orange-600 dark:text-orange-400"
                      : remainingChars < 50
                      ? "text-red-600 dark:text-red-400"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {remainingChars} characters remaining
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-dark-700">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-700 border border-gray-300 dark:border-dark-600 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Submitting...</span>
                  </>
                ) : (
                  <span>Submit Feedback</span>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
