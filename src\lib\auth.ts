import { supabase } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface AuthUser extends User {
  profile?: {
    full_name: string | null;
    avatar_url: string | null;
    role: "student" | "lecturer" | "admin";
    institution: string | null;
  };
}

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
}

export interface SignInData {
  email: string;
  password: string;
}

// Authentication functions
export const authService = {
  // Sign up new user with email existence check
  async signUp(data: SignUpData) {
    const { email, password, fullName } = data;

    // First check if the email already exists in the profiles table
    const { data: existingProfile, error: checkError } = await supabase
      .from("profiles")
      .select("id, email")
      .eq("email", email)
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      // PGRST116 is "not found" error, other errors should be handled
      console.warn(
        "Error checking email existence during registration:",
        checkError
      );
    }

    if (existingProfile) {
      // Email already exists in the system
      throw new Error(
        "An account with this email address already exists. Please sign in instead or use a different email address."
      );
    }

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          role: "student", // Default role for all users
        },
      },
    });

    if (authError) {
      // Handle specific Supabase auth errors
      if (authError.message.includes("User already registered")) {
        throw new Error(
          "An account with this email address already exists. Please sign in instead."
        );
      }
      throw authError;
    }

    // Try to create profile record, but don't fail if table doesn't exist
    if (authData.user) {
      try {
        const { error: profileError } = await supabase.from("profiles").insert({
          id: authData.user.id,
          email: authData.user.email!,
          full_name: fullName,
          role: "student", // Default role for all users
        });

        // Only throw error if it's not a "table doesn't exist" error
        if (profileError && !profileError.message.includes("does not exist")) {
          throw profileError;
        }
      } catch (error: any) {
        // Log the error but don't fail registration
        console.warn(
          "Profile creation failed (table may not exist):",
          error.message
        );
      }
    }

    return authData;
  },

  // Sign in user with specific error messages
  async signIn(data: SignInData) {
    const { email, password } = data;

    // First check if the email exists in the profiles table
    const { data: existingProfile, error: checkError } = await supabase
      .from("profiles")
      .select("id, email")
      .eq("email", email)
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      // PGRST116 is "not found" error, other errors should be handled
      console.warn("Error checking email existence:", checkError);
    }

    if (!existingProfile) {
      // Email doesn't exist in the system
      throw new Error(
        "This email address is not registered. Please sign up first or check your email address."
      );
    }

    // Email exists, now try to sign in
    const { data: authData, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // If email exists but login fails, it's likely a password issue
      if (error.message.includes("Invalid login credentials")) {
        throw new Error(
          "Incorrect password. Please check your password and try again."
        );
      }
      throw error;
    }

    // Check if user is active after successful authentication
    if (authData.user) {
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("active, deactivation_reason, deactivated_at")
        .eq("id", authData.user.id)
        .single();

      if (profileError) {
        console.error("Error checking user status:", profileError);
        // Continue with login if we can't check status (backward compatibility)
      } else if (profile && profile.active === false) {
        console.log("User is deactivated, profile data:", profile); // Debug log

        // User is deactivated, sign them out and throw specific error
        await supabase.auth.signOut();
        const deactivationError = new Error("Account deactivated") as any;
        deactivationError.code = "ACCOUNT_DEACTIVATED";
        deactivationError.details = {
          reason: profile.deactivation_reason || "No reason provided",
          deactivatedAt: profile.deactivated_at,
          email: authData.user.email,
        };

        console.log(
          "Throwing deactivation error with details:",
          deactivationError.details
        ); // Debug log
        throw deactivationError;
      }
    }

    return authData;
  },

  // Sign out user
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) throw error;
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    });
    if (error) throw error;
  },

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("getCurrentUser timeout")), 3000);
      });

      const getUserPromise = supabase.auth.getUser();

      const {
        data: { user },
      } = await Promise.race([getUserPromise, timeoutPromise]);

      if (!user) {
        return null;
      }

      // Always use user metadata as fallback since profiles table may not exist
      const profile = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || null,
        role: user.user_metadata?.role || "student",
        institution: user.user_metadata?.institution || null,
        avatar_url: null,
        created_at: user.created_at,
        updated_at: user.updated_at || user.created_at,
      };

      const authUser = {
        ...user,
        profile,
      } as AuthUser;

      return authUser;
    } catch (error: any) {
      console.error("Error in getCurrentUser:", error);
      return null;
    }
  },

  // Get auth state change listener
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (_event, session) => {
      if (session?.user) {
        try {
          // Use session data directly instead of calling getCurrentUser
          const user = session.user;
          const profile = {
            id: user.id,
            email: user.email,
            full_name: user.user_metadata?.full_name || null,
            role: user.user_metadata?.role || "student",
            institution: user.user_metadata?.institution || null,
            avatar_url: null,
            created_at: user.created_at,
            updated_at: user.updated_at || user.created_at,
          };

          const authUser = {
            ...user,
            profile,
          } as AuthUser;

          callback(authUser);
        } catch (error) {
          console.error("Error creating user from session:", error);
          callback(null);
        }
      } else {
        callback(null);
      }
    });
  },
};
