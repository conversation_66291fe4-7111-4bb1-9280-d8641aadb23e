-- Test Script for Room Creation Fix
-- This script tests the room creation functionality after applying the fix

-- ============================================================================
-- PREPARATION: Clean up any test data
-- ============================================================================

-- Remove any test rooms and members (be careful in production!)
DELETE FROM room_members WHERE room_id IN (
    SELECT id FROM rooms WHERE name LIKE 'TEST_ROOM_%'
);
DELETE FROM rooms WHERE name LIKE 'TEST_ROOM_%';

-- ============================================================================
-- TEST 1: Basic Room Creation Test
-- ============================================================================

DO $$
DECLARE
    test_user_id UUID;
    test_room_id UUID;
    member_count INTEGER;
    creator_is_admin BOOLEAN;
BEGIN
    -- Get a test user ID (use the first available user)
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'No users found in auth.users table for testing';
    END IF;
    
    RAISE NOTICE 'Testing room creation with user ID: %', test_user_id;
    
    -- Test room creation
    INSERT INTO rooms (name, description, room_code, created_by, is_private, max_members)
    VALUES (
        'TEST_ROOM_' || EXTRACT(EPOCH FROM NOW())::TEXT,
        'Test room for trigger validation',
        'TEST' || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0'),
        test_user_id,
        false,
        50
    )
    RETURNING id INTO test_room_id;
    
    RAISE NOTICE 'Room created successfully with ID: %', test_room_id;
    
    -- Check if the creator was automatically added as admin
    SELECT COUNT(*) INTO member_count
    FROM room_members 
    WHERE room_id = test_room_id;
    
    SELECT EXISTS(
        SELECT 1 FROM room_members 
        WHERE room_id = test_room_id 
        AND user_id = test_user_id 
        AND role = 'admin'
    ) INTO creator_is_admin;
    
    -- Report results
    RAISE NOTICE '=== TEST RESULTS ===';
    RAISE NOTICE 'Room created: YES';
    RAISE NOTICE 'Members added automatically: %', member_count;
    RAISE NOTICE 'Creator is admin: %', CASE WHEN creator_is_admin THEN 'YES' ELSE 'NO' END;
    
    -- Validate results
    IF member_count = 0 THEN
        RAISE EXCEPTION 'FAILED: No members were added to the room';
    END IF;
    
    IF NOT creator_is_admin THEN
        RAISE EXCEPTION 'FAILED: Creator was not added as admin';
    END IF;
    
    RAISE NOTICE 'SUCCESS: Room creation trigger is working correctly!';
    
    -- Clean up test data
    DELETE FROM room_members WHERE room_id = test_room_id;
    DELETE FROM rooms WHERE id = test_room_id;
    
    RAISE NOTICE 'Test data cleaned up successfully';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Room creation test FAILED: %', SQLERRM;
END $$;

-- ============================================================================
-- TEST 2: Multiple Room Creation Test
-- ============================================================================

DO $$
DECLARE
    test_user_id UUID;
    i INTEGER;
    success_count INTEGER := 0;
    total_tests INTEGER := 3;
BEGIN
    -- Get a test user ID
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    RAISE NOTICE 'Testing multiple room creations...';
    
    -- Create multiple rooms to test consistency
    FOR i IN 1..total_tests LOOP
        BEGIN
            INSERT INTO rooms (name, description, room_code, created_by, is_private, max_members)
            VALUES (
                'TEST_ROOM_MULTI_' || i || '_' || EXTRACT(EPOCH FROM NOW())::TEXT,
                'Multi-test room ' || i,
                'MT' || LPAD(i::TEXT, 2, '0') || LPAD(FLOOR(RANDOM() * 100)::TEXT, 2, '0'),
                test_user_id,
                false,
                25
            );
            
            success_count := success_count + 1;
            RAISE NOTICE 'Room % created successfully', i;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Room % creation failed: %', i, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '=== MULTIPLE CREATION TEST RESULTS ===';
    RAISE NOTICE 'Successful creations: % out of %', success_count, total_tests;
    
    IF success_count = total_tests THEN
        RAISE NOTICE 'SUCCESS: All room creations worked correctly!';
    ELSE
        RAISE WARNING 'Some room creations failed';
    END IF;
    
    -- Clean up test data
    DELETE FROM room_members WHERE room_id IN (
        SELECT id FROM rooms WHERE name LIKE 'TEST_ROOM_MULTI_%'
    );
    DELETE FROM rooms WHERE name LIKE 'TEST_ROOM_MULTI_%';
    
    RAISE NOTICE 'Multi-test data cleaned up successfully';
END $$;

-- ============================================================================
-- TEST 3: Error Handling Test
-- ============================================================================

DO $$
DECLARE
    test_user_id UUID;
    error_caught BOOLEAN := false;
BEGIN
    RAISE NOTICE 'Testing error handling...';
    
    -- Test with NULL created_by (should fail gracefully)
    BEGIN
        INSERT INTO rooms (name, description, room_code, created_by, is_private, max_members)
        VALUES (
            'TEST_ROOM_ERROR',
            'This should fail',
            'ERROR1',
            NULL,  -- This should cause an error
            false,
            25
        );
        
        RAISE NOTICE 'ERROR: Room creation with NULL created_by should have failed!';
        
    EXCEPTION
        WHEN OTHERS THEN
            error_caught := true;
            RAISE NOTICE 'Good: Error correctly caught for NULL created_by: %', SQLERRM;
    END;
    
    RAISE NOTICE '=== ERROR HANDLING TEST RESULTS ===';
    RAISE NOTICE 'Error handling works: %', CASE WHEN error_caught THEN 'YES' ELSE 'NO' END;
    
    IF error_caught THEN
        RAISE NOTICE 'SUCCESS: Error handling is working correctly!';
    ELSE
        RAISE WARNING 'Error handling may not be working as expected';
    END IF;
END $$;

-- ============================================================================
-- FINAL STATUS CHECK
-- ============================================================================

-- ============================================================================
-- FINAL STATUS CHECK
-- ============================================================================

DO $$
DECLARE
    trigger_count INTEGER;
    problematic_count INTEGER;
BEGIN
    -- Check current trigger status
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers
    WHERE event_object_table = 'rooms'
    AND trigger_name = 'trigger_add_room_creator_as_admin';

    -- Check for any remaining problematic functions
    SELECT COUNT(*) INTO problematic_count
    FROM pg_proc
    WHERE prosrc LIKE '%NEW.user_id%';

    RAISE NOTICE '=== FINAL STATUS CHECK ===';
    RAISE NOTICE 'Room creation trigger exists: %', CASE WHEN trigger_count > 0 THEN 'YES' ELSE 'NO' END;
    RAISE NOTICE 'Problematic functions remaining: %', problematic_count;

    RAISE NOTICE '=== ROOM CREATION FIX TEST COMPLETED ===';
    RAISE NOTICE 'If all tests passed, room creation should now work in the application';
    RAISE NOTICE 'You can now try creating a room through the SmartBagPack interface';
END $$;

-- Show detailed status for reference
SELECT
    'FINAL TRIGGER STATUS' as status_type,
    COUNT(*) as trigger_count
FROM information_schema.triggers
WHERE event_object_table = 'rooms'
AND trigger_name = 'trigger_add_room_creator_as_admin';

SELECT
    'PROBLEMATIC FUNCTIONS CHECK' as status_type,
    COUNT(*) as problematic_count
FROM pg_proc
WHERE prosrc LIKE '%NEW.user_id%';
