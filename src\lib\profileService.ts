import { supabase } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface CreateProfileData {
  id: string;
  email: string;
  full_name?: string;
  role?: "student" | "lecturer" | "admin";
  institution?: string;
  avatar_url?: string;
}

export const profileService = {
  // Create a profile for a new user (fallback if trigger fails)
  async createUserProfile(userData: CreateProfileData): Promise<void> {
    try {
      const profileData = {
        id: userData.id,
        email: userData.email,
        full_name: userData.full_name || userData.email.split("@")[0],
        role: userData.role || "student",
        institution: userData.institution || null,
        avatar_url: userData.avatar_url || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("profiles")
        .insert(profileData)
        .single();

      if (error) {
        // If it's a duplicate key error, that's okay - profile already exists
        if (error.code === "23505") {
          return;
        }
        throw error;
      }
    } catch (error: any) {
      console.error("Error creating user profile:", error);
      throw new Error(`Failed to create user profile: ${error.message}`);
    }
  },

  // Ensure a user has a profile (create if missing)
  async ensureUserProfile(user: User): Promise<void> {
    try {
      // Check if profile exists
      const { data: existingProfile, error: checkError } = await supabase
        .from("profiles")
        .select("id")
        .eq("id", user.id)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        throw checkError;
      }

      // If profile doesn't exist, create it
      if (!existingProfile) {
        await this.createUserProfile({
          id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || user.user_metadata?.name,
          role: "student", // Default role for all users
          avatar_url: user.user_metadata?.avatar_url,
        });
      }
    } catch (error: any) {
      console.error("Error ensuring user profile:", error);
      // Don't throw here - we don't want to break the user's session
    }
  },

  // Get user profile
  async getUserProfile(userId: string): Promise<{
    id: string;
    email: string;
    full_name: string | null;
    role: string;
    institution: string | null;
    avatar_url: string | null;
    created_at: string;
    updated_at: string;
  }> {
    try {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          throw new Error("Profile not found");
        }
        throw error;
      }

      if (!profile) {
        throw new Error("Profile not found");
      }

      return profile;
    } catch (error: any) {
      console.error("Error getting user profile:", error);
      throw new Error(`Failed to get user profile: ${error.message}`);
    }
  },

  // Update user profile
  async updateUserProfile(
    userId: string,
    updates: Partial<Omit<CreateProfileData, "id" | "email">>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Error updating user profile:", error);
      throw new Error(`Failed to update user profile: ${error.message}`);
    }
  },

  // Delete user account and all associated data (including auth record)
  async deleteUserAccount(userId: string): Promise<void> {
    try {
      // Get user's documents for storage cleanup before deletion
      const { data: documents } = await supabase
        .from("documents")
        .select("file_path")
        .eq("user_id", userId);

      // Delete user documents from storage first
      if (documents && documents.length > 0) {
        const filePaths = documents.map((doc) => doc.file_path).filter(Boolean);
        if (filePaths.length > 0) {
          const { error: storageError } = await supabase.storage
            .from("documents")
            .remove(filePaths);

          if (storageError) {
            console.error(
              "Error deleting user files from storage:",
              storageError
            );
          }
        }
      }

      // Use the database function to delete all user data from application tables
      // Use delete_own_account for self-deletion to avoid JSON concatenation issues
      const { data: deletionResult, error: dbError } = await supabase.rpc(
        "delete_own_account",
        {
          p_user_id: userId,
        }
      );

      if (dbError) {
        throw new Error(
          `Database error deleting user data: ${dbError.message}`
        );
      }

      // Now delete the user from auth.users using the admin client
      // This is the critical step that allows complete account deletion
      const { supabaseAdmin } = await import("./supabase");

      if (!supabaseAdmin) {
        throw new Error(
          "Complete account deletion requires admin configuration. Please contact support."
        );
      }

      const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(
        userId
      );

      if (authError) {
        console.error("Error deleting user from auth:", authError);
        throw new Error(
          `Failed to completely delete account: ${authError.message}. Your data has been removed but you may need to contact support to fully delete your authentication record.`
        );
      }

      // Sign out the user since their account has been completely deleted
      await supabase.auth.signOut();

      console.log("Complete user account deletion successful:", deletionResult);
    } catch (error: any) {
      console.error("Error deleting user account:", error);
      throw new Error(`Failed to delete user account: ${error.message}`);
    }
  },

  // Get profile statistics (for admin use)
  async getProfileStats(): Promise<{
    totalProfiles: number;
    totalAuthUsers: number;
    students: number;
    lecturers: number;
    admins: number;
    missingProfiles: number;
  }> {
    try {
      const { data, error } = await supabase.rpc("get_profile_stats");

      if (error) throw error;

      // Convert the result to a more usable format
      const stats = data.reduce((acc: any, row: any) => {
        switch (row.stat_name) {
          case "Total Profiles":
            acc.totalProfiles = row.stat_value;
            break;
          case "Total Auth Users":
            acc.totalAuthUsers = row.stat_value;
            break;
          case "Students":
            acc.students = row.stat_value;
            break;
          case "Lecturers":
            acc.lecturers = row.stat_value;
            break;
          case "Admins":
            acc.admins = row.stat_value;
            break;
          case "Users Missing Profiles":
            acc.missingProfiles = row.stat_value;
            break;
        }
        return acc;
      }, {});

      return stats;
    } catch (error: any) {
      console.error("Error getting profile stats:", error);
      throw new Error(`Failed to get profile stats: ${error.message}`);
    }
  },

  // Migrate missing users (admin function)
  async migrateMissingUsers(): Promise<number> {
    try {
      const { data, error } = await supabase.rpc("migrate_missing_users");

      if (error) throw error;

      return data;
    } catch (error: any) {
      console.error("Error migrating missing users:", error);
      throw new Error(`Failed to migrate missing users: ${error.message}`);
    }
  },

  // Check if user has a complete profile
  async hasCompleteProfile(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, full_name, role")
        .eq("id", userId)
        .single();

      if (error) return false;

      // Consider profile complete if it has a name and role
      return !!(data?.full_name && data?.role);
    } catch (error) {
      return false;
    }
  },
};

// Hook for React components to ensure profile exists
export const useEnsureProfile = () => {
  const ensureCurrentUserProfile = async (user: User | null) => {
    if (user) {
      await profileService.ensureUserProfile(user);
    }
  };

  return { ensureCurrentUserProfile };
};
