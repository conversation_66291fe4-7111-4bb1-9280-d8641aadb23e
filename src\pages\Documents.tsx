import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { FileList } from "../components/files/FileList";
import {
  FileFiltersComponent,
  applyFilters,
} from "../components/files/FileFilters";
import type { FileFilters } from "../components/files/FileFilters";

import { EnhancedShareModal } from "../components/files/EnhancedShareModal";
import { BulkShareToRoom } from "../components/files/BulkShareToRoom";
import { QuickShareToRoom } from "../components/files/QuickShareToRoom";
import { FolderBreadcrumb } from "../components/folders/FolderBreadcrumb";
import { FolderList } from "../components/folders/FolderList";
import { FolderActions } from "../components/folders/FolderActions";
import { FolderModal } from "../components/folders/FolderModal";

import { fileService } from "../lib/fileService";
import { folderService } from "../lib/folderService";
import type { DocumentFile } from "../lib/fileService";
import type { Folder, FolderWithChildren } from "../lib/folderService";
import { useAuth } from "../contexts/AuthContext";
import toast from "react-hot-toast";
import { usePageTitle } from "../hooks/usePageTitle";

export function Documents() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Set page title
  usePageTitle("My Documents");
  const [documents, setDocuments] = useState<DocumentFile[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<DocumentFile[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);

  const [shareDocument, setShareDocument] = useState<DocumentFile | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentFile[]>(
    []
  );
  const [showBulkShare, setShowBulkShare] = useState(false);
  const [quickShareDocument, setQuickShareDocument] =
    useState<DocumentFile | null>(null);
  const [quickShareRoomId, setQuickShareRoomId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  // const [statistics, setStatistics] = useState({
  //   totalDocuments: 0,
  //   publicDocuments: 0,
  //   totalDownloads: 0,
  //   storageUsed: 0,
  // });
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [filters, setFilters] = useState<FileFilters>({
    search: "",
    fileType: "",
    sortBy: "date",
    sortOrder: "desc",
    dateRange: "all",
    sizeRange: "all",
    isPublic: null,
    sharingStatus: null,
  });

  // Folder-related state
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState<Folder | null>(null);
  const [folders, setFolders] = useState<FolderWithChildren[]>([]);
  const [breadcrumbPath, setBreadcrumbPath] = useState<Folder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(true);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [folderModal, setFolderModal] = useState<{
    isOpen: boolean;
    folder: Folder | null;
    action: "delete" | "create" | null;
  }>({
    isOpen: false,
    folder: null,
    action: null,
  });

  useEffect(() => {
    loadDocuments();
    loadFolders();
    loadStatistics();

    // Check for shareToRoom query parameter
    const shareToRoom = searchParams.get("shareToRoom");
    if (shareToRoom) {
      setQuickShareRoomId(shareToRoom);
      // Clear the query parameter
      setSearchParams({});
    }
  }, []);

  // Load data when current folder changes
  useEffect(() => {
    if (user) {
      loadDocuments();
      loadFolders();
      loadBreadcrumb();
    }
  }, [currentFolderId, user]);

  useEffect(() => {
    // Show quick share modal when both document and room are selected
    if (quickShareRoomId && documents.length > 0) {
      // For now, we'll show a toast to select a document
      // In a real implementation, you might want to highlight documents or show a selection UI
      toast.success("Select a document to share to the room");
    }
  }, [quickShareRoomId, documents]);

  useEffect(() => {
    // Reload statistics when documents change
    if (user) {
      loadStatistics();
    }
  }, [documents, user]);

  useEffect(() => {
    const filtered = applyFilters(documents, filters, user?.id);
    setFilteredDocuments(filtered);
  }, [documents, filters, user?.id]);

  const loadDocuments = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Load documents in current folder (includes shared documents since they're now owned by user)
      const folderDocuments = await fileService.getDocumentsByFolder(
        user.id,
        currentFolderId
      );

      // Since shared documents are now permanently owned by the user,
      // they're already included in getDocumentsByFolder results
      // No need to load and combine shared documents separately
      const allDocuments = folderDocuments;

      setDocuments(allDocuments);
    } catch (error: any) {
      console.error("Load documents error:", error);
      // Check if it's the RLS recursion error
      if (
        error.message?.includes("infinite recursion") ||
        error.message?.includes("policy")
      ) {
        toast.error(
          "Database configuration issue detected. Please check the database policies."
        );
      } else {
        toast.error(error.message || "Failed to load documents");
      }
      // Set empty array to prevent app crash
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStatistics = async () => {
    if (!user) return;

    try {
      setIsLoadingStats(true);
      // const stats = await fileService.getUserStatistics(user.id);
      // setStatistics(stats);
    } catch (error: any) {
      console.error("Load statistics error:", error);
      // Don't show error toast for statistics as it's not critical
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Folder-related functions
  const loadFolders = async () => {
    if (!user) return;

    try {
      setIsLoadingFolders(true);
      const folderHierarchy = await folderService.getFolderHierarchy(
        user.id,
        currentFolderId
      );
      setFolders(folderHierarchy);
    } catch (error: any) {
      console.error("Load folders error:", error);
      toast.error("Failed to load folders");
    } finally {
      setIsLoadingFolders(false);
    }
  };

  const loadBreadcrumb = async () => {
    if (!user || !currentFolderId) {
      setBreadcrumbPath([]);
      setCurrentFolder(null);
      return;
    }

    try {
      const [folder, breadcrumb] = await Promise.all([
        folderService.getFolderById(currentFolderId, user.id),
        folderService.getFolderBreadcrumb(currentFolderId, user.id),
      ]);
      setCurrentFolder(folder);
      setBreadcrumbPath(breadcrumb);
    } catch (error: any) {
      console.error("Load breadcrumb error:", error);
      // Don't show error toast as this is not critical
    }
  };

  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId);
  };

  const handleNavigateToFolder = (folderId: string | null) => {
    setCurrentFolderId(folderId);
  };

  const handleFolderCreated = (folder: Folder) => {
    loadFolders();
    loadStatistics(); // Refresh statistics
    setFolderModal({ isOpen: false, folder: null, action: null });
    toast.success(`Folder "${folder.name}" created successfully`);
  };

  const handleFolderUpdated = (folder: Folder) => {
    loadFolders();
    setEditingFolder(null);
    toast.success(`Folder "${folder.name}" updated successfully`);
  };

  const handleFolderEdit = (folder: FolderWithChildren) => {
    setEditingFolder(folder);
  };

  const handleFolderDelete = (folder: FolderWithChildren) => {
    setFolderModal({
      isOpen: true,
      folder,
      action: "delete",
    });
  };

  const handleFolderDeleted = () => {
    loadFolders();
    setFolderModal({ isOpen: false, folder: null, action: null });
  };

  // Drag and drop handlers
  const handleDocumentDrag = async (
    documentIds: string[],
    targetFolderId: string | null
  ) => {
    if (!user) return;

    try {
      await fileService.moveDocumentsToFolder(
        documentIds,
        targetFolderId,
        user.id
      );
      toast.success(
        `Moved ${documentIds.length} document(s) to ${
          targetFolderId ? "folder" : "root"
        }`
      );
      loadDocuments(); // Refresh the document list
    } catch (error: any) {
      console.error("Move documents error:", error);
      toast.error(error.message || "Failed to move documents");
    }
  };

  const handleFolderDrop = async (dragItem: any, dropTarget: any) => {
    if (!user) return;

    try {
      if (dragItem.type === "document") {
        // Move document to folder
        await handleDocumentDrag([dragItem.id], dropTarget.id);
      } else if (dragItem.type === "folder") {
        // Move folder (update parent_folder_id)
        await folderService.updateFolder(
          dragItem.id,
          { parent_folder_id: dropTarget.id },
          user.id
        );
        toast.success(`Moved folder "${dragItem.data.name}" successfully`);
        loadFolders();
      }
    } catch (error: any) {
      console.error("Drop operation error:", error);
      toast.error(error.message || "Failed to move item");
    }
  };

  const handleDocumentUpdate = (updatedDocument: DocumentFile) => {
    setDocuments((prev) =>
      prev.map((doc) => (doc.id === updatedDocument.id ? updatedDocument : doc))
    );
    // Refresh statistics when a document is updated
    if (user) {
      loadStatistics();
    }
  };

  const handleDocumentDelete = (documentIds: string[]) => {
    // Remove from UI immediately
    setDocuments((prev) => {
      const filtered = prev.filter((doc) => !documentIds.includes(doc.id));
      return filtered;
    });

    // Refresh statistics and documents list to ensure accuracy
    if (user) {
      setTimeout(() => {
        loadStatistics();
        loadDocuments(); // Reload to ensure database is in sync
      }, 500); // Small delay to ensure deletion is complete
    }
  };

  // Function to refresh all data (can be called when user returns to page)
  const refreshData = () => {
    loadDocuments();
    loadFolders();
    loadStatistics();
  };

  const handleShareComplete = () => {
    setShareDocument(null);
    toast.success("Document shared successfully!");
  };

  const handleBulkShareToRoom = (documents: DocumentFile[]) => {
    setSelectedDocuments(documents);
    setShowBulkShare(true);
  };

  const handleBulkShareComplete = () => {
    setSelectedDocuments([]);
    setShowBulkShare(false);
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading documents...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  My Documents
                </h1>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                  Manage and organize your uploaded documents
                </p>
              </div>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                <button
                  onClick={refreshData}
                  disabled={isLoading || isLoadingStats}
                  className="btn-secondary flex items-center justify-center space-x-2"
                  title="Refresh data"
                >
                  <svg
                    className={`w-4 h-4 ${
                      isLoading || isLoadingStats ? "animate-spin" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  <span>Refresh</span>
                </button>
                {/* Hide upload button on mobile - will be in FAB instead */}
                <button
                  onClick={() => navigate("/upload")}
                  className="btn-primary hidden lg:flex items-center justify-center space-x-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  <span>Upload Documents</span>
                </button>
              </div>
            </div>
          </div>

          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <FolderBreadcrumb
              currentFolder={currentFolder}
              breadcrumbPath={breadcrumbPath}
              onNavigateToFolder={handleNavigateToFolder}
            />
          </div>

          {/* Folder Actions */}
          <div className="mb-6">
            <FolderActions
              currentFolderId={currentFolderId}
              onFolderCreated={handleFolderCreated}
              onFolderUpdated={handleFolderUpdated}
              editingFolder={editingFolder}
              onCancelEdit={() => setEditingFolder(null)}
            />
          </div>

          {/* Stats Cards - Only show when at root level */}
          {/* {!currentFolderId && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="card p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Total Documents
                    </p>
                    <div className="text-2xl font-semibold text-gray-900">
                      {isLoadingStats ? (
                        <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                      ) : (
                        statistics.totalDocuments
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="card p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Public Documents
                    </p>
                    <div className="text-2xl font-semibold text-gray-900">
                      {isLoadingStats ? (
                        <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                      ) : (
                        statistics.publicDocuments
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="card p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-purple-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Total Downloads
                    </p>
                    <div className="text-2xl font-semibold text-gray-900">
                      {isLoadingStats ? (
                        <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                      ) : (
                        statistics.totalDownloads
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="card p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-orange-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Storage Used
                    </p>
                    <div className="text-2xl font-semibold text-gray-900">
                      {isLoadingStats ? (
                        <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                      ) : statistics.storageUsed === 0 ? (
                        "0 MB"
                      ) : statistics.storageUsed >= 1024 * 1024 * 1024 ? (
                        `${(
                          statistics.storageUsed /
                          (1024 * 1024 * 1024)
                        ).toFixed(1)} GB`
                      ) : statistics.storageUsed >= 1024 * 1024 ? (
                        `${(statistics.storageUsed / (1024 * 1024)).toFixed(
                          1
                        )} MB`
                      ) : (
                        `${(statistics.storageUsed / 1024).toFixed(1)} KB`
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )} */}

          {/* Filters */}
          <div className="mb-6">
            <FileFiltersComponent
              filters={filters}
              onFiltersChange={setFilters}
              documents={documents}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>

          {/* Results Summary */}
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Showing {filteredDocuments.length} of {documents.length} documents
            </p>
          </div>

          {/* Folder List */}
          {!isLoadingFolders && folders.length > 0 && (
            <div className="mb-8">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Folders
              </h2>
              <FolderList
                folders={folders}
                onFolderClick={handleFolderClick}
                onFolderEdit={handleFolderEdit}
                onFolderDelete={handleFolderDelete}
                onDrop={handleFolderDrop}
                onRefresh={loadFolders}
                viewMode={viewMode}
                showActions={true}
                enableDragAndDrop={true}
              />
            </div>
          )}

          {/* Document List */}
          <div className="mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
              {folders.length > 0 ? "Documents" : "All Documents"}
            </h2>
          </div>
          <FileList
            documents={filteredDocuments}
            onDocumentUpdate={handleDocumentUpdate}
            onDocumentDelete={handleDocumentDelete}
            onDocumentShare={setShareDocument}
            onBulkShareToRoom={handleBulkShareToRoom}
            onQuickShareToRoom={setQuickShareDocument}
            onRefresh={loadDocuments}
            quickShareRoomId={quickShareRoomId}
            viewMode={viewMode}
            showActions={true}
            enableDragAndDrop={true}
          />
        </div>
      </div>

      {/* Enhanced Share Modal */}
      {shareDocument && (
        <EnhancedShareModal
          document={shareDocument}
          onClose={() => setShareDocument(null)}
          onShareComplete={handleShareComplete}
        />
      )}

      {/* Bulk Share to Room Modal */}
      {showBulkShare && selectedDocuments.length > 0 && (
        <BulkShareToRoom
          documents={selectedDocuments}
          onClose={() => setShowBulkShare(false)}
          onShareComplete={handleBulkShareComplete}
        />
      )}

      {/* Quick Share to Room Modal */}
      {quickShareDocument && quickShareRoomId && (
        <QuickShareToRoom
          document={quickShareDocument}
          roomId={quickShareRoomId}
          onClose={() => {
            setQuickShareDocument(null);
            setQuickShareRoomId(null);
          }}
          onShareComplete={() => {
            setQuickShareDocument(null);
            setQuickShareRoomId(null);
          }}
        />
      )}

      {/* Folder Modal */}
      <FolderModal
        isOpen={folderModal.isOpen}
        onClose={() =>
          setFolderModal({ isOpen: false, folder: null, action: null })
        }
        folder={folderModal.folder}
        action={folderModal.action}
        onFolderDeleted={handleFolderDeleted}
        onFolderCreated={handleFolderCreated}
        currentFolderId={currentFolderId}
      />
    </DashboardLayout>
  );
}
