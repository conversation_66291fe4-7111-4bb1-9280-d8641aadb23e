// Admin service for SmartBagPack - handles admin operations safely
import { supabase, supabaseAdmin } from "../supabase";
import type {
  SystemStats,
  AdminUser,
  UserFilters,
  PaginatedResponse,
} from "../../types/admin";
import type { UserRole } from "../../types/shared";
import { isAdminRole } from "./adminUtils";
import { logAdminAction } from "./adminAuth";

/**
 * Verify admin access before performing operations
 */
async function verifyAdminAccess(): Promise<boolean> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return false;

    // Check metadata first
    if (user.user_metadata?.role === "admin") return true;

    // Fallback to profile check
    const { data: profile } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    return isAdminRole(profile?.role);
  } catch {
    return false;
  }
}

/**
 * Get system-wide statistics for admin dashboard
 */
export async function getSystemStats(): Promise<SystemStats> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Get current admin user ID to exclude from total count
    const {
      data: { user: currentUser },
    } = await supabase.auth.getUser();

    if (!currentUser) {
      throw new Error("Unable to get current user");
    }

    // Get user statistics
    const { data: userStats, error: userError } = await supabase
      .from("profiles")
      .select("id, role, created_at");

    if (userError) throw userError;

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Filter out the current admin user from all counts
    const filteredUserStats =
      userStats?.filter((u) => u.id !== currentUser.id) || [];

    const users = {
      total_users: filteredUserStats.length,
      new_users_30d: filteredUserStats.filter(
        (u) => new Date(u.created_at) > thirtyDaysAgo
      ).length,
      admin_count: filteredUserStats.filter((u) => u.role === "admin").length,
      lecturer_count: filteredUserStats.filter((u) => u.role === "lecturer")
        .length,
      student_count: filteredUserStats.filter((u) => u.role === "student")
        .length,
      active_users_7d: 0, // Will be implemented with activity tracking
    };

    // Get document statistics
    const { data: documents, error: docError } = await supabase
      .from("documents")
      .select("file_size, created_at, user_id");

    if (docError) throw docError;

    const totalStorageUsed =
      documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;
    const recentUploads =
      documents?.filter((d) => new Date(d.created_at) > sevenDaysAgo).length ||
      0;
    const uniqueUploaders = new Set(documents?.map((d) => d.user_id)).size;

    const documentStats = {
      total_documents: documents?.length || 0,
      total_storage_used: totalStorageUsed,
      recent_uploads: recentUploads,
      active_uploaders: uniqueUploaders,
      avg_file_size: documents?.length
        ? totalStorageUsed / documents.length
        : 0,
    };

    // Get room statistics
    const { data: rooms, error: roomError } = await supabase
      .from("rooms")
      .select("is_private, created_at");

    if (roomError) throw roomError;

    const { data: roomMembers, error: memberError } = await supabase
      .from("room_members")
      .select("room_id");

    if (memberError) throw memberError;

    const roomMemberCounts =
      roomMembers?.reduce((acc, member) => {
        acc[member.room_id] = (acc[member.room_id] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

    const avgMembersPerRoom =
      Object.keys(roomMemberCounts).length > 0
        ? Object.values(roomMemberCounts).reduce(
            (sum, count) => sum + count,
            0
          ) / Object.keys(roomMemberCounts).length
        : 0;

    const roomStats = {
      total_rooms: rooms?.length || 0,
      public_rooms: rooms?.filter((r) => !r.is_private).length || 0,
      private_rooms: rooms?.filter((r) => r.is_private).length || 0,
      new_rooms_30d:
        rooms?.filter((r) => new Date(r.created_at) > thirtyDaysAgo).length ||
        0,
      avg_members_per_room: avgMembersPerRoom,
      active_rooms_7d: 0, // Will be implemented with activity tracking
    };

    // Storage statistics
    const totalCapacity = users.total_users * 200 * 1024 * 1024; // 200MB per user
    const storage = {
      total_used: totalStorageUsed,
      total_capacity: totalCapacity,
      avg_per_user:
        users.total_users > 0 ? totalStorageUsed / users.total_users : 0,
      usage_percentage:
        totalCapacity > 0 ? (totalStorageUsed / totalCapacity) * 100 : 0,
    };

    return {
      users,
      documents: documentStats,
      rooms: roomStats,
      storage,
    };
  } catch (error: any) {
    throw new Error(`Failed to get system stats: ${error.message}`);
  }
}

/**
 * Get all users with admin information
 */
export async function getAllUsers(
  filters?: UserFilters,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<AdminUser>> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Get current admin user to exclude from list
    const { data: currentUser } = await supabase.auth.getUser();

    let query = supabase
      .from("profiles")
      .select("*, active, deactivation_reason, deactivated_at", {
        count: "exact",
      });

    // Exclude current admin user
    if (currentUser.user?.id) {
      query = query.neq("id", currentUser.user.id);
    }

    // Apply filters
    if (filters?.search) {
      query = query.or(
        `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`
      );
    }
    if (filters?.registration_date_from) {
      query = query.gte("created_at", filters.registration_date_from);
    }
    if (filters?.registration_date_to) {
      query = query.lte("created_at", filters.registration_date_to);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: profiles, error, count } = await query;

    if (error) throw error;

    // Get user statistics for each user
    const usersWithStats: AdminUser[] = await Promise.all(
      (profiles || []).map(async (profile) => {
        const { data: userDocs } = await supabase
          .from("documents")
          .select("file_size")
          .eq("user_id", profile.id);

        const { data: userRooms } = await supabase
          .from("room_members")
          .select("room_id")
          .eq("user_id", profile.id);

        const { data: createdRooms } = await supabase
          .from("rooms")
          .select("id")
          .eq("created_by", profile.id);

        const documentCount = userDocs?.length || 0;
        const storageUsed =
          userDocs?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;

        return {
          ...profile,
          user_stats: {
            document_count: documentCount,
            storage_used: storageUsed,
            rooms_joined: userRooms?.length || 0,
            rooms_created: createdRooms?.length || 0,
            last_activity: null, // Will be implemented with activity tracking
            registration_date: profile.created_at,
          },
        };
      })
    );

    return {
      data: usersWithStats,
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error: any) {
    throw new Error(`Failed to get users: ${error.message}`);
  }
}

/**
 * Update user role (admin operation)
 */
export async function updateUserRole(
  userId: string,
  newRole: UserRole
): Promise<void> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const { data: currentUser } = await supabase.auth.getUser();
    if (currentUser.user?.id === userId) {
      throw new Error("Cannot change your own role");
    }

    // Get current role for logging
    const { data: currentProfile } = await supabase
      .from("profiles")
      .select("role, full_name")
      .eq("id", userId)
      .single();

    if (!currentProfile) {
      throw new Error("User not found");
    }

    // Update role
    const { error } = await supabase
      .from("profiles")
      .update({ role: newRole, updated_at: new Date().toISOString() })
      .eq("id", userId);

    if (error) throw error;

    // Log admin action
    const logResult = await logAdminAction("user_role_change", "user", userId, {
      old_role: currentProfile.role,
      new_role: newRole,
      user_name: currentProfile.full_name,
    });

    if (!logResult.success) {
      console.error("Failed to log admin action:", logResult.error);
    }
  } catch (error: any) {
    throw new Error(`Failed to update user role: ${error.message}`);
  }
}

/**
 * Deactivate a user account (admin operation)
 */
export async function deactivateUser(
  userId: string,
  reason?: string
): Promise<void> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    console.log("Deactivating user with reason:", reason); // Debug log

    // Use admin function to update user status (bypasses RLS)
    const { data, error } = await supabase.rpc("admin_update_user_status", {
      p_user_id: userId,
      p_active: false,
      p_reason: reason || "No reason provided",
    });

    console.log("Database function result:", { data, error }); // Debug log

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data?.success) {
      throw new Error(data?.error || "Failed to deactivate user");
    }

    // Note: The database function handles both the update and logging
  } catch (error: any) {
    console.error("Deactivation error:", error); // Debug log
    throw new Error(`Failed to deactivate user: ${error.message}`);
  }
}

/**
 * Activate a user account (admin operation)
 */
export async function activateUser(userId: string): Promise<void> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Use admin function to update user status (bypasses RLS)
    const { data, error } = await supabase.rpc("admin_update_user_status", {
      p_user_id: userId,
      p_active: true,
      p_reason: "Account activated by admin",
    });

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data?.success) {
      throw new Error(data?.error || "Failed to activate user");
    }

    // Note: The database function handles both the update and logging
  } catch (error: any) {
    throw new Error(`Failed to activate user: ${error.message}`);
  }
}

/**
 * Delete a user account and all associated data (admin operation)
 */
export async function deleteUser(userId: string): Promise<void> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Get user's documents for storage cleanup before deletion
    const { data: userDocuments } = await supabase
      .from("documents")
      .select("file_path")
      .eq("user_id", userId);

    // Delete user's documents from storage
    if (userDocuments && userDocuments.length > 0) {
      const filePaths = userDocuments.map((doc) => doc.file_path);
      const { error: storageError } = await supabase.storage
        .from("documents")
        .remove(filePaths);

      if (storageError) {
        console.error("Error deleting user files from storage:", storageError);
      }
    }

    // First, use the database function to safely delete user data from all tables
    const { data: deletionResult, error: dbError } = await supabase.rpc(
      "admin_delete_user",
      {
        p_user_id: userId,
      }
    );

    if (dbError) {
      throw new Error(`Database error deleting user data: ${dbError.message}`);
    }

    // Now delete the user from auth.users using Supabase Admin API
    // This is the critical step that was missing
    if (!supabaseAdmin) {
      console.warn(
        "Admin client not available - user auth record will not be deleted"
      );
      console.warn(
        "Please set VITE_SUPABASE_SERVICE_ROLE_KEY environment variable"
      );
    }

    const { error: authError } = supabaseAdmin
      ? await supabaseAdmin.auth.admin.deleteUser(userId)
      : { error: new Error("Admin client not available") };

    if (authError) {
      console.error("Error deleting user from auth:", authError);
      // If auth deletion fails, we should still consider the operation successful
      // since the user data has been cleaned up from all application tables
      console.warn("User data deleted but auth record may still exist");
    }

    // Log the admin action
    const logResult = await logAdminAction("delete", "user", userId, {
      user_id: userId,
      deletion_result: deletionResult,
      auth_deletion_success: !authError,
      timestamp: new Date().toISOString(),
    });

    if (!logResult.success) {
      console.error("Failed to log admin action:", logResult.error);
    }
  } catch (error: any) {
    throw new Error(`Failed to delete user: ${error.message}`);
  }
}

/**
 * Get user activity logs
 */
export async function getUserActivity(
  userId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<any>> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from("activity_logs")
      .select("*", { count: "exact" })
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error: any) {
    throw new Error(`Failed to get user activity: ${error.message}`);
  }
}

/**
 * Get admin action history
 */
export async function getAdminActionHistory(
  page: number = 1,
  limit: number = 50
): Promise<PaginatedResponse<any>> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from("admin_actions")
      .select(
        `
        *,
        profiles!admin_actions_admin_id_fkey(full_name)
      `,
        { count: "exact" }
      )
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error: any) {
    throw new Error(`Failed to get admin action history: ${error.message}`);
  }
}

/**
 * Bulk update user roles (admin operation)
 */
export async function bulkUpdateUserRoles(
  userIds: string[],
  newRole: UserRole
): Promise<{ success: number; failed: number; errors: string[] }> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Use the database function for bulk role updates
    const { data, error } = await supabase.rpc("admin_bulk_update_roles", {
      p_user_ids: userIds,
      p_new_role: newRole,
    });

    if (error) throw error;

    return {
      success: data.success || 0,
      failed: data.failed || 0,
      errors: data.errors || [],
    };
  } catch (error: any) {
    throw new Error(`Failed to bulk update user roles: ${error.message}`);
  }
}

/**
 * Bulk delete users (admin operation)
 */
export async function bulkDeleteUsers(
  userIds: string[]
): Promise<{ success: number; failed: number; errors: string[] }> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  const results = { success: 0, failed: 0, errors: [] as string[] };

  // Process each user individually since we need to handle storage cleanup
  for (const userId of userIds) {
    try {
      await deleteUser(userId);
      results.success++;
    } catch (error: any) {
      results.failed++;
      results.errors.push(`User ${userId}: ${error.message}`);
    }
  }

  // Log bulk action (the individual deletions are already logged by the database function)
  const logResult = await logAdminAction("bulk_action", "user", undefined, {
    action: "bulk_delete",
    user_count: userIds.length,
    success_count: results.success,
    failed_count: results.failed,
  });

  if (!logResult.success) {
    console.error("Failed to log bulk admin action:", logResult.error);
  }

  return results;
}

/**
 * Get user details with comprehensive statistics
 */
export async function getUserDetails(
  userId: string
): Promise<AdminUser | null> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error || !profile) return null;

    // Get comprehensive user statistics
    const [userDocs, userRooms, createdRooms, sharedDocs, recentActivity] =
      await Promise.all([
        supabase
          .from("documents")
          .select("file_size, created_at")
          .eq("user_id", userId),
        supabase
          .from("room_members")
          .select("room_id, joined_at")
          .eq("user_id", userId),
        supabase
          .from("rooms")
          .select("id, name, created_at")
          .eq("created_by", userId),
        supabase
          .from("document_shares")
          .select("*")
          .eq("shared_with_user_id", userId),
        supabase
          .from("activity_logs")
          .select("*")
          .eq("user_id", userId)
          .order("created_at", { ascending: false })
          .limit(10),
      ]);

    const documentCount = userDocs.data?.length || 0;
    const storageUsed =
      userDocs.data?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;
    const recentUploads =
      userDocs.data?.filter(
        (doc) =>
          new Date(doc.created_at) >
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length || 0;

    return {
      ...profile,
      user_stats: {
        document_count: documentCount,
        storage_used: storageUsed,
        rooms_joined: userRooms.data?.length || 0,
        rooms_created: createdRooms.data?.length || 0,
        documents_shared_with_user: sharedDocs.data?.length || 0,
        recent_uploads_7d: recentUploads,
        last_activity: recentActivity.data?.[0]?.created_at || null,
        registration_date: profile.created_at,
      },
    };
  } catch (error: any) {
    throw new Error(`Failed to get user details: ${error.message}`);
  }
}

// Export the admin service
export const adminService = {
  getSystemStats,
  getAllUsers,
  updateUserRole,
  deactivateUser,
  activateUser,
  deleteUser,
  getUserActivity,
  getUserDetails,
  bulkUpdateUserRoles,
  bulkDeleteUsers,
  getAdminActionHistory,
};
