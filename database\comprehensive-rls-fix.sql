-- Comprehensive RLS Fix for User Deletion Side Effects
-- This fixes the RLS violations caused by orphaned auth users (users deleted from profiles but not auth.users)

-- ============================================================================
-- PART 1: Fix Profile RLS Policies to Handle Orphaned Users
-- ============================================================================

-- Drop problematic policies that require active status
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Create safe profile policies that handle missing profiles gracefully
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (
  auth.uid() = id
  -- Removed AND active = true to allow orphaned users to be handled
);

CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (
  auth.uid() = id AND active = true
) WITH CHECK (
  auth.uid() = id AND active = true
);

-- Allow profile creation for orphaned users (auto-recovery)
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (
  auth.uid() = id
);

-- ============================================================================
-- PART 2: Fix Documents RLS Policies
-- ============================================================================

-- Drop existing document policies
DROP POLICY IF EXISTS "Users can create documents" ON documents;
DROP POLICY IF EXISTS "Users can create documents and shared copies" ON documents;

-- Create robust document creation policy that handles orphaned users
CREATE POLICY "Users can create documents" ON documents FOR INSERT WITH CHECK (
  auth.uid() = user_id
  AND (
    -- User has active profile
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND active = true)
    OR
    -- OR user is orphaned (has auth but no profile) - allow with auto-profile creation
    NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid())
  )
);

-- ============================================================================
-- PART 3: Fix Folders RLS Policies  
-- ============================================================================

-- Drop existing folder policies
DROP POLICY IF EXISTS "Users can create folders" ON folders;

-- Create robust folder creation policy
CREATE POLICY "Users can create folders" ON folders FOR INSERT WITH CHECK (
  auth.uid() = user_id
  AND (
    -- User has active profile
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND active = true)
    OR
    -- OR user is orphaned - allow with auto-profile creation
    NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid())
  )
);

-- ============================================================================
-- PART 4: Fix Rooms RLS Policies
-- ============================================================================

-- Drop existing room policies
DROP POLICY IF EXISTS "Users can create rooms" ON rooms;

-- Create robust room creation policy
CREATE POLICY "Users can create rooms" ON rooms FOR INSERT WITH CHECK (
  auth.uid() = created_by
  AND (
    -- User has active profile
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND active = true)
    OR
    -- OR user is orphaned - allow with auto-profile creation
    NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid())
  )
);

-- ============================================================================
-- PART 5: Auto-Profile Creation Function for Orphaned Users
-- ============================================================================

-- Function to automatically create profile for orphaned users
CREATE OR REPLACE FUNCTION auto_create_profile_for_orphaned_user()
RETURNS TRIGGER AS $$
DECLARE
  user_email TEXT;
BEGIN
  -- Get user email from auth.users
  SELECT email INTO user_email FROM auth.users WHERE id = NEW.user_id;
  
  -- Create profile if user doesn't have one
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = NEW.user_id) THEN
    INSERT INTO profiles (id, email, full_name, role, active, created_at, updated_at)
    VALUES (
      NEW.user_id,
      COALESCE(user_email, '<EMAIL>'),
      'Recovered User',
      'student',
      true,
      NOW(),
      NOW()
    );
    
    -- Log the auto-recovery
    INSERT INTO activity_logs (user_id, action_type, target_type, target_id, details)
    VALUES (
      NEW.user_id,
      'profile_auto_created',
      'user',
      NEW.user_id,
      jsonb_build_object(
        'reason', 'orphaned_user_recovery',
        'email', user_email,
        'timestamp', NOW()
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- PART 6: Create Triggers for Auto-Recovery
-- ============================================================================

-- Trigger on documents table to auto-create profile for orphaned users
DROP TRIGGER IF EXISTS trigger_auto_create_profile_documents ON documents;
CREATE TRIGGER trigger_auto_create_profile_documents
  BEFORE INSERT ON documents
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_profile_for_orphaned_user();

-- Trigger on folders table to auto-create profile for orphaned users  
DROP TRIGGER IF EXISTS trigger_auto_create_profile_folders ON folders;
CREATE TRIGGER trigger_auto_create_profile_folders
  BEFORE INSERT ON folders
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_profile_for_orphaned_user();

-- Trigger on rooms table to auto-create profile for orphaned users
DROP TRIGGER IF EXISTS trigger_auto_create_profile_rooms ON rooms;
CREATE TRIGGER trigger_auto_create_profile_rooms
  BEFORE INSERT ON rooms
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_profile_for_orphaned_user();

-- ============================================================================
-- PART 7: Cleanup Function for Orphaned Users
-- ============================================================================

-- Function to clean up orphaned auth users (admin use)
CREATE OR REPLACE FUNCTION cleanup_orphaned_auth_users()
RETURNS JSON AS $$
DECLARE
  orphaned_count INTEGER := 0;
  cleaned_count INTEGER := 0;
  result JSON;
BEGIN
  -- Count orphaned users (auth.users without profiles)
  SELECT COUNT(*) INTO orphaned_count
  FROM auth.users au
  WHERE NOT EXISTS (SELECT 1 FROM profiles p WHERE p.id = au.id)
  AND au.email != '<EMAIL>'; -- Protect admin user
  
  -- Note: This function only counts. Actual cleanup requires service role key
  -- and should be done through the admin API
  
  result := json_build_object(
    'orphaned_users_found', orphaned_count,
    'message', 'Use admin API with service role key to actually delete orphaned auth users',
    'timestamp', NOW()
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION auto_create_profile_for_orphaned_user() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_orphaned_auth_users() TO authenticated;
