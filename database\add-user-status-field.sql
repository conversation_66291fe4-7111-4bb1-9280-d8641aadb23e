-- Add active status field to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT true;

-- Update existing users to be active by default
UPDATE profiles SET active = true WHERE active IS NULL;

-- Create index for better performance on active status queries
CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles(active);

-- Update RLS policies to respect active status for authentication
-- Users can only access their own profile if they are active
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (
  auth.uid() = id AND active = true
);

-- Users can update their own profile only if they are active
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (
  auth.uid() = id AND active = true
) WITH CHECK (
  auth.uid() = id AND active = true
);

-- <PERSON><PERSON> can view all profiles regardless of status
CREATE POLICY "Admin can view all profiles" ON profiles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND email = '<EMAIL>'
  )
);

-- <PERSON><PERSON> can update any profile
CREATE POLICY "Admin can update any profile" ON profiles FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND email = '<EMAIL>'
  )
);

-- Create function to automatically log out deactivated users
CREATE OR REPLACE FUNCTION check_user_active_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If user is being deactivated, we'll handle logout in the application
  -- This trigger just ensures data consistency
  IF NEW.active = false AND OLD.active = true THEN
    -- Log the deactivation
    INSERT INTO activity_logs (user_id, action_type, target_type, target_id, details)
    VALUES (
      NEW.id,
      'user_deactivated',
      'user',
      NEW.id,
      jsonb_build_object(
        'deactivated_at', NOW(),
        'previous_status', 'active'
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user status changes
DROP TRIGGER IF EXISTS trigger_user_status_change ON profiles;
CREATE TRIGGER trigger_user_status_change
  AFTER UPDATE OF active ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION check_user_active_status();
