-- Fix Shared Documents Display Issue
-- This script ensures that shared documents are properly marked and can be displayed in the "Shared with Me" list

-- Step 1: Update existing shared documents that might be missing the is_shared_copy flag
-- Look for documents that have corresponding entries in document_shares but aren't marked as shared copies
UPDATE documents 
SET 
  is_shared_copy = true,
  shared_at = COALESCE(shared_at, NOW())
WHERE id IN (
  SELECT DISTINCT ds.shared_document_id 
  FROM document_shares ds 
  WHERE ds.shared_document_id IS NOT NULL
    AND ds.is_permanent = true
) AND is_shared_copy = false;

-- Step 2: Update shared_from_user_id for documents that are missing this information
UPDATE documents 
SET shared_from_user_id = ds.shared_by
FROM document_shares ds
WHERE documents.id = ds.shared_document_id 
  AND documents.is_shared_copy = true 
  AND documents.shared_from_user_id IS NULL
  AND ds.is_permanent = true;

-- Step 3: Update original_document_id for shared documents that are missing this information
UPDATE documents 
SET original_document_id = ds.original_document_id
FROM document_shares ds
WHERE documents.id = ds.shared_document_id 
  AND documents.is_shared_copy = true 
  AND documents.original_document_id IS NULL
  AND ds.is_permanent = true;

-- Step 4: Verify the fix by checking shared documents
DO $$
DECLARE
  shared_count INTEGER;
  properly_marked_count INTEGER;
BEGIN
  -- Count total shared documents
  SELECT COUNT(*) INTO shared_count
  FROM document_shares 
  WHERE shared_document_id IS NOT NULL AND is_permanent = true;
  
  -- Count properly marked shared documents
  SELECT COUNT(*) INTO properly_marked_count
  FROM documents 
  WHERE is_shared_copy = true;
  
  RAISE NOTICE 'Total shared documents in document_shares: %', shared_count;
  RAISE NOTICE 'Documents marked as shared copies: %', properly_marked_count;
  
  IF shared_count > 0 AND properly_marked_count = 0 THEN
    RAISE WARNING 'No documents are marked as shared copies despite having share records';
  ELSIF shared_count > properly_marked_count THEN
    RAISE WARNING 'Some shared documents may not be properly marked';
  ELSE
    RAISE NOTICE 'Shared documents appear to be properly configured';
  END IF;
END $$;
