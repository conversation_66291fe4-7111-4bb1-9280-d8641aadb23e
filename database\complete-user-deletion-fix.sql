-- Complete User Deletion Fix
-- Run this SQL in your Supabase SQL Editor to fix all user deletion issues

-- First, drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS admin_delete_user(UUID);
DROP FUNCTION IF EXISTS admin_update_user_status(UUID, BOOLEAN, TEXT);

-- Create the fixed admin_delete_user function
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  admin_actions INTEGER := 0;
BEGIN
  -- Check if current user is admin OR deleting their own account
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') 
     AND auth.uid() != p_user_id THEN
    RAISE EXCEPTION 'Access denied: Admin role required or can only delete own account';
  END IF;
  
  -- Allow self-deletion for regular users, but prevent admin self-deletion
  IF auth.uid() = p_user_id AND EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Admin users cannot delete their own account';
  END IF;
  
  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Count records before deletion for logging
  SELECT COUNT(*) INTO documents FROM documents WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO folders FROM folders WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO room_memberships FROM room_members WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO rooms_created FROM rooms WHERE created_by = p_user_id;
  SELECT COUNT(*) INTO document_shares FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  SELECT COUNT(*) INTO activity_logs FROM activity_logs WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO admin_actions FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Build deletion summary
  deleted_counts := json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'admin_actions_removed', admin_actions
  );
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete admin actions
  DELETE FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user (FIXED: use shared_by not user_id)
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications (FIXED: notifications table only has user_id, no sender_id)
  DELETE FROM notifications WHERE user_id = p_user_id;
  
  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;
  
  -- Delete room invitations (FIXED: use invited_user not invited_user_id)
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;
  
  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;
  
  -- Note: We don't delete from auth.users here as it's handled by the client-side admin API
  -- This ensures proper cleanup of the authentication record
  
  -- Log the admin action (only if not self-deletion)
  IF auth.uid() != p_user_id THEN
    PERFORM log_admin_action(
      'user_delete',
      'user',
      p_user_id,
      deleted_counts
    );
  END IF;
  
  RETURN deleted_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the admin_update_user_status function
CREATE OR REPLACE FUNCTION admin_update_user_status(
  p_user_id UUID,
  p_active BOOLEAN,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_admin_id UUID;
  v_user_record RECORD;
  v_result JSON;
BEGIN
  -- Get the current admin user ID
  v_admin_id := auth.uid();
  
  -- Verify admin access
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = v_admin_id 
    AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;
  
  -- Get user record before update
  SELECT * INTO v_user_record FROM profiles WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Update user status (bypasses RLS)
  UPDATE profiles 
  SET 
    active = p_active,
    deactivation_reason = CASE WHEN p_active THEN NULL ELSE p_reason END,
    deactivated_at = CASE WHEN p_active THEN NULL ELSE NOW() END,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Log the action
  INSERT INTO activity_logs (
    user_id,
    action_type,
    target_type,
    target_id,
    details
  ) VALUES (
    v_admin_id,
    CASE WHEN p_active THEN 'user_activate' ELSE 'user_deactivate' END,
    'user',
    p_user_id,
    jsonb_build_object(
      'target_user_email', v_user_record.email,
      'target_user_name', v_user_record.full_name,
      'previous_status', v_user_record.active,
      'new_status', p_active,
      'reason', COALESCE(p_reason, 'No reason provided'),
      'timestamp', NOW()
    )
  );
  
  -- Return success result
  v_result := json_build_object(
    'success', true,
    'message', CASE WHEN p_active THEN 'User activated successfully' ELSE 'User deactivated successfully' END,
    'user_id', p_user_id,
    'new_status', p_active
  );
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_update_user_status(UUID, BOOLEAN, TEXT) TO authenticated;

-- Test the function (optional - remove if you don't want to test)
-- SELECT 'User deletion functions updated successfully. Key fixes:
-- 1. Fixed room_documents column name (shared_by not user_id)
-- 2. Fixed room_invitations column name (invited_user not invited_user_id)  
-- 3. Added support for self-deletion by regular users
-- 4. Prevented admin self-deletion
-- 5. Added proper room invitation links cleanup' as status;
