-- COMPREHENSIVE FIX FOR ROOM CREATION TRIGGER ISSUE
-- This script completely resolves the 'record "new" has no field "user_id"' error
-- by systematically removing all conflicting functions and creating the correct ones

-- ============================================================================
-- STEP 1: COMPLETE CLEANUP OF EXISTING TRIGGERS AND FUNCTIONS
-- ============================================================================

-- Drop ALL existing triggers on rooms table (regardless of name)
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN
        SELECT trigger_name
        FROM information_schema.triggers
        WHERE event_object_table = 'rooms'
    LOOP
        RAISE NOTICE 'Dropping trigger: %', trigger_record.trigger_name;
        EXECUTE 'DROP TRIGGER IF EXISTS ' || trigger_record.trigger_name || ' ON rooms CASCADE';
    END LOOP;
END $$;

-- Drop ALL functions that might reference NEW.user_id (the problematic pattern)
DO $$
DECLARE
    func_record RECORD;
BEGIN
    FOR func_record IN
        SELECT proname
        FROM pg_proc
        WHERE prosrc LIKE '%NEW.user_id%'
    LOOP
        RAISE NOTICE 'Dropping problematic function with NEW.user_id: %', func_record.proname;
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.proname || '() CASCADE';
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.proname || '(text) CASCADE';
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.proname || '(uuid) CASCADE';
    END LOOP;
END $$;

-- Drop all room-related functions to ensure clean slate
DROP FUNCTION IF EXISTS add_room_creator_as_admin() CASCADE;
DROP FUNCTION IF EXISTS add_room_creator_as_admin(text) CASCADE;
DROP FUNCTION IF EXISTS add_room_creator_as_admin(uuid) CASCADE;
DROP FUNCTION IF EXISTS add_room_creator() CASCADE;
DROP FUNCTION IF EXISTS room_creator_handler() CASCADE;
DROP FUNCTION IF EXISTS handle_room_creation() CASCADE;
DROP FUNCTION IF EXISTS room_admin_trigger() CASCADE;

-- ============================================================================
-- STEP 2: SCHEMA VALIDATION
-- ============================================================================

-- Validate that the rooms table has the correct structure
DO $$
BEGIN
    -- Check if created_by column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'rooms' AND column_name = 'created_by'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: rooms table is missing created_by column';
    END IF;

    -- Check if room_members table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'room_members'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: room_members table does not exist';
    END IF;

    -- Check if room_members has user_id column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'room_members' AND column_name = 'user_id'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: room_members table is missing user_id column';
    END IF;

    RAISE NOTICE 'Schema validation passed - all required tables and columns exist';
END $$;

-- ============================================================================
-- STEP 3: CREATE THE CORRECT TRIGGER FUNCTION
-- ============================================================================

-- Create the correct function that uses NEW.created_by (NOT NEW.user_id)
CREATE OR REPLACE FUNCTION add_room_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
    -- Detailed logging for debugging
    RAISE NOTICE 'Room creation trigger fired for room ID: %', NEW.id;
    RAISE NOTICE 'Room creator (created_by): %', NEW.created_by;

    -- Validate that NEW record has the required fields
    IF NEW.id IS NULL THEN
        RAISE EXCEPTION 'Room ID cannot be null';
    END IF;

    IF NEW.created_by IS NULL THEN
        RAISE EXCEPTION 'Room creator (created_by) cannot be null';
    END IF;

    -- Insert the room creator as admin in room_members table
    -- IMPORTANT: Using NEW.created_by (NOT NEW.user_id)
    INSERT INTO room_members (room_id, user_id, role)
    VALUES (NEW.id, NEW.created_by, 'admin');

    RAISE NOTICE 'Successfully added room creator as admin';
    RETURN NEW;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in add_room_creator_as_admin trigger: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 4: CREATE THE TRIGGER
-- ============================================================================

-- Create the trigger
CREATE TRIGGER trigger_add_room_creator_as_admin
    AFTER INSERT ON rooms
    FOR EACH ROW
    EXECUTE FUNCTION add_room_creator_as_admin();

-- ============================================================================
-- STEP 5: COMPREHENSIVE VERIFICATION AND TESTING
-- ============================================================================

-- Verify the fix was applied correctly
DO $$
DECLARE
    trigger_count INTEGER;
    function_exists BOOLEAN;
    problematic_functions INTEGER;
BEGIN
    -- Check if the trigger was created
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers
    WHERE trigger_name = 'trigger_add_room_creator_as_admin'
    AND event_object_table = 'rooms';

    -- Check if the function exists
    SELECT EXISTS(
        SELECT 1 FROM pg_proc
        WHERE proname = 'add_room_creator_as_admin'
    ) INTO function_exists;

    -- Check for any remaining problematic functions
    SELECT COUNT(*) INTO problematic_functions
    FROM pg_proc
    WHERE prosrc LIKE '%NEW.user_id%';

    -- Report results
    RAISE NOTICE '=== VERIFICATION RESULTS ===';
    RAISE NOTICE 'Room creation trigger exists: %', CASE WHEN trigger_count > 0 THEN 'YES' ELSE 'NO' END;
    RAISE NOTICE 'Room creation function exists: %', CASE WHEN function_exists THEN 'YES' ELSE 'NO' END;
    RAISE NOTICE 'Problematic functions remaining: %', problematic_functions;

    -- Validate success
    IF trigger_count = 0 THEN
        RAISE EXCEPTION 'FAILED: Room creation trigger was not created';
    END IF;

    IF NOT function_exists THEN
        RAISE EXCEPTION 'FAILED: Room creation function was not created';
    END IF;

    IF problematic_functions > 0 THEN
        RAISE WARNING 'WARNING: % function(s) still contain NEW.user_id references', problematic_functions;
    END IF;

    RAISE NOTICE '=== SUCCESS ===';
    RAISE NOTICE 'Room creation trigger has been fixed successfully!';
    RAISE NOTICE 'The trigger now correctly uses NEW.created_by field';
    RAISE NOTICE 'All conflicting functions have been removed';
    RAISE NOTICE 'Room creation should now work without errors';
END $$;

-- ============================================================================
-- STEP 6: FINAL DIAGNOSTIC INFORMATION
-- ============================================================================

-- Show the current trigger configuration
SELECT
    'CURRENT ROOM TRIGGERS' as info_type,
    trigger_name,
    action_timing,
    event_manipulation,
    action_statement
FROM information_schema.triggers
WHERE event_object_table = 'rooms';

-- Show the function source to confirm it's correct
SELECT
    'TRIGGER FUNCTION SOURCE' as info_type,
    proname as function_name,
    prosrc as function_source
FROM pg_proc
WHERE proname = 'add_room_creator_as_admin';
