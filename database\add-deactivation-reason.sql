-- Add deactivation reason and timestamp to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS deactivation_reason TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS deactivated_at TIMESTAMP WITH TIME ZONE;

-- Drop existing function to ensure clean update
DROP FUNCTION IF EXISTS admin_update_user_status(UUID, BOOLEAN, TEXT);
DROP FUNCTION IF EXISTS admin_update_user_status(UUID, BOOLEAN);

-- Update the admin function to store deactivation reason in profiles table
CREATE OR REPLACE FUNCTION admin_update_user_status(
  p_user_id UUID,
  p_active BOOLEAN,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_admin_id UUID;
  v_user_record RECORD;
  v_result JSON;
BEGIN
  -- Get the current admin user ID
  v_admin_id := auth.uid();
  
  -- Verify admin access
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = v_admin_id 
    AND email = '<EMAIL>'
  ) THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;
  
  -- Get user record before update
  SELECT * INTO v_user_record FROM profiles WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Update user status (bypasses RLS because function is SECURITY DEFINER)
  IF p_active THEN
    -- Activating user - clear deactivation fields
    UPDATE profiles 
    SET 
      active = true,
      deactivation_reason = NULL,
      deactivated_at = NULL,
      updated_at = NOW()
    WHERE id = p_user_id;
  ELSE
    -- Deactivating user - set deactivation fields
    UPDATE profiles 
    SET 
      active = false,
      deactivation_reason = COALESCE(p_reason, 'No reason provided'),
      deactivated_at = NOW(),
      updated_at = NOW()
    WHERE id = p_user_id;
  END IF;
  
  -- Log the action in activity_logs if table exists
  BEGIN
    INSERT INTO activity_logs (
      user_id,
      action_type,
      target_type,
      target_id,
      details
    ) VALUES (
      v_admin_id,
      CASE WHEN p_active THEN 'user_activate' ELSE 'user_deactivate' END,
      'user',
      p_user_id,
      jsonb_build_object(
        'target_user_email', v_user_record.email,
        'target_user_name', v_user_record.full_name,
        'previous_status', COALESCE(v_user_record.active, true),
        'new_status', p_active,
        'reason', COALESCE(p_reason, 'No reason provided'),
        'timestamp', NOW()
      )
    );
  EXCEPTION
    WHEN undefined_table THEN
      -- activity_logs table doesn't exist, skip logging
      NULL;
  END;
  
  -- Return success result
  v_result := json_build_object(
    'success', true,
    'message', CASE WHEN p_active THEN 'User activated successfully' ELSE 'User deactivated successfully' END,
    'user_id', p_user_id,
    'new_status', p_active
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Return error result
    v_result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'code', SQLSTATE
    );
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
