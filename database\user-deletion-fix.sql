-- Fixed User Self-Deletion System
-- This script fixes the JSON concatenation error in the user deletion functions

-- Drop existing functions to recreate them
DROP FUNCTION IF EXISTS delete_own_account(UUID);
DROP FUNCTION IF EXISTS admin_delete_user(UUID);

-- Create a simplified function for user self-deletion
CREATE OR REPLACE FUNCTION delete_own_account(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_email TEXT;
  user_name TEXT;
  documents_count INTEGER := 0;
  folders_count INTEGER := 0;
  room_memberships_count INTEGER := 0;
  rooms_created_count INTEGER := 0;
  document_shares_count INTEGER := 0;
  activity_logs_count INTEGER := 0;
  notifications_count INTEGER := 0;
  feedback_count INTEGER := 0;
  room_invitations_count INTEGER := 0;
  room_invitation_links_count INTEGER := 0;
  shared_documents_nullified_count INTEGER := 0;
BEGIN
  -- Verify that the user is deleting their own account
  IF auth.uid() != p_user_id THEN
    RAISE EXCEPTION 'Access denied: You can only delete your own account';
  END IF;
  
  -- Prevent admin users from deleting their own accounts
  IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'admin') THEN
    RAISE EXCEPTION 'Admin users cannot delete their own account. Please contact another admin.';
  END IF;
  
  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Count records before deletion for logging
  SELECT COUNT(*) INTO documents_count FROM documents WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO folders_count FROM folders WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO room_memberships_count FROM room_members WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO rooms_created_count FROM rooms WHERE created_by = p_user_id;
  SELECT COUNT(*) INTO document_shares_count FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  SELECT COUNT(*) INTO activity_logs_count FROM activity_logs WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO notifications_count FROM notifications WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO feedback_count FROM user_feedback WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO room_invitations_count FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;
  SELECT COUNT(*) INTO room_invitation_links_count FROM room_invitation_links WHERE created_by = p_user_id;
  SELECT COUNT(*) INTO shared_documents_nullified_count FROM documents WHERE shared_from_user_id = p_user_id;
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications
  DELETE FROM notifications WHERE user_id = p_user_id;

  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;

  -- Delete room invitations
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;

  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;

  -- Return deletion summary
  RETURN json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'deletion_type', 'self_deletion',
    'documents_deleted', documents_count,
    'folders_deleted', folders_count,
    'room_memberships_removed', room_memberships_count,
    'rooms_deleted', rooms_created_count,
    'document_shares_removed', document_shares_count,
    'activity_logs_removed', activity_logs_count,
    'notifications_removed', notifications_count,
    'feedback_removed', feedback_count,
    'room_invitations_removed', room_invitations_count,
    'room_invitation_links_removed', room_invitation_links_count,
    'shared_documents_nullified', shared_documents_nullified_count,
    'deleted_at', NOW(),
    'deleted_by', auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simplified admin_delete_user function
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deletion_result JSON;
  is_self_deletion BOOLEAN := false;
  is_admin_deletion BOOLEAN := false;
BEGIN
  -- Check if this is self-deletion
  IF auth.uid() = p_user_id THEN
    is_self_deletion := true;
    -- Prevent admin self-deletion
    IF EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Admin users cannot delete their own account. Please contact another admin.';
    END IF;
  ELSE
    -- Check if current user is admin for admin deletion
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Access denied: Admin role required to delete other users';
    END IF;
    is_admin_deletion := true;
  END IF;
  
  -- Use the self-deletion function for consistency
  SELECT delete_own_account(p_user_id) INTO deletion_result;
  
  -- Update deletion type in the result
  IF is_admin_deletion THEN
    deletion_result := json_build_object(
      'user_email', deletion_result->>'user_email',
      'user_name', deletion_result->>'user_name',
      'deletion_type', 'admin_deletion',
      'documents_deleted', deletion_result->>'documents_deleted',
      'folders_deleted', deletion_result->>'folders_deleted',
      'room_memberships_removed', deletion_result->>'room_memberships_removed',
      'rooms_deleted', deletion_result->>'rooms_deleted',
      'document_shares_removed', deletion_result->>'document_shares_removed',
      'activity_logs_removed', deletion_result->>'activity_logs_removed',
      'notifications_removed', deletion_result->>'notifications_removed',
      'feedback_removed', deletion_result->>'feedback_removed',
      'room_invitations_removed', deletion_result->>'room_invitations_removed',
      'room_invitation_links_removed', deletion_result->>'room_invitation_links_removed',
      'shared_documents_nullified', deletion_result->>'shared_documents_nullified',
      'deleted_at', deletion_result->>'deleted_at',
      'deleted_by', auth.uid()
    );
  END IF;
  
  -- Log admin action (only for admin deletions, not self-deletions)
  IF is_admin_deletion THEN
    PERFORM log_admin_action(
      'user_delete',
      'user',
      p_user_id,
      deletion_result::JSONB
    );
  END IF;
  
  RETURN deletion_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION delete_own_account(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;
