-- Final RLS Policy Fix for Document Sharing
-- Run this in your Supabase SQL editor

-- 1. Update documents table policies to be more permissive for shared documents
DROP POLICY IF EXISTS "Users can view shared documents" ON documents;

CREATE POLICY "Users can view shared documents" ON documents FOR SELECT USING (
  -- Own documents
  auth.uid() = user_id
  OR
  -- Public documents  
  is_public = true
  OR
  -- Documents shared with user (new schema)
  id IN (
    SELECT original_document_id FROM document_shares 
    WHERE shared_with = auth.uid() AND original_document_id IS NOT NULL
  )
  OR
  -- Documents shared with user (old schema - backward compatibility)
  id IN (
    SELECT document_id FROM document_shares 
    WHERE shared_with = auth.uid() AND document_id IS NOT NULL
  )
  OR
  -- Documents shared through rooms
  id IN (
    SELECT rd.document_id FROM room_documents rd
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

-- 2. Update document_shares policies to allow proper access
DROP POLICY IF EXISTS "Users can view shares involving them" ON document_shares;

CREATE POLICY "Users can view shares involving them" ON document_shares FOR SELECT USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

-- 3. Ensure storage policies allow access to shared files
DROP POLICY IF EXISTS "Users can view own files and shared files" ON storage.objects;

CREATE POLICY "Users can view own files and shared files" ON storage.objects
FOR SELECT USING (
  -- Own files (in their folder)
  (storage.foldername(name))[1] = auth.uid()::text
  OR
  -- Files from documents shared with them
  name IN (
    SELECT d.file_path 
    FROM documents d
    WHERE d.id IN (
      SELECT original_document_id FROM document_shares 
      WHERE shared_with = auth.uid() AND original_document_id IS NOT NULL
      UNION
      SELECT document_id FROM document_shares 
      WHERE shared_with = auth.uid() AND document_id IS NOT NULL
    )
  )
  OR
  -- Files in rooms they're members of
  name IN (
    SELECT d.file_path 
    FROM documents d
    JOIN room_documents rd ON d.id = rd.document_id
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

-- 4. Verify the policies are working
-- You can run these queries to test:

-- Test 1: Check if user can see their own documents
-- SELECT id, title FROM documents WHERE user_id = auth.uid();

-- Test 2: Check if user can see documents shared with them
-- SELECT d.id, d.title, ds.shared_by 
-- FROM documents d 
-- JOIN document_shares ds ON (d.id = ds.original_document_id OR d.id = ds.document_id)
-- WHERE ds.shared_with = auth.uid();

-- Test 3: Check document_shares access
-- SELECT * FROM document_shares WHERE shared_with = auth.uid() OR shared_by = auth.uid();
