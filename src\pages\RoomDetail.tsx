import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { RoomDocumentList } from "../components/rooms/RoomDocumentList";
import { RoomActivityFeed } from "../components/rooms/RoomActivityFeed";
import { RoomSettings } from "../components/rooms/RoomSettings";
import { InviteUsersModal } from "../components/invitations/InviteUsersModal";
import { CreateInviteLinkModal } from "../components/invitations/CreateInviteLinkModal";
import { roomService } from "../lib/roomService";
import type {
  RoomWithDetails,
  RoomMember,
  RoomDocument,
} from "../lib/roomService";
import { useAuth } from "../contexts/AuthContext";
import toast from "react-hot-toast";
import { formatDateTime } from "../lib/utils";
import { usePageTitle } from "../hooks/usePageTitle";

export function RoomDetail() {
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [room, setRoom] = useState<
    (RoomWithDetails & { members: RoomMember[] }) | null
  >(null);
  const [documents, setDocuments] = useState<RoomDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [documentsLoading, setDocumentsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTab, setSelectedTab] = useState<
    "documents" | "members" | "activity"
  >("documents");
  const [showSettings, setShowSettings] = useState(false);
  const [isLeavingRoom, setIsLeavingRoom] = useState(false);
  const [showInviteUsers, setShowInviteUsers] = useState(false);
  const [showCreateLink, setShowCreateLink] = useState(false);

  // Set page title based on room name
  usePageTitle(room ? `Room: ${room.name}` : "Room");

  useEffect(() => {
    if (roomId && user) {
      loadRoomDetails();
      loadRoomDocuments();
    }
  }, [roomId, user]);

  const loadRoomDetails = async () => {
    if (!roomId || !user) return;

    try {
      setIsLoading(true);
      const roomDetails = await roomService.getRoomDetails(roomId, user.id);
      setRoom(roomDetails);
    } catch (error: any) {
      console.error("Load room details error:", error);
      toast.error(error.message || "Failed to load room details");
      navigate("/rooms");
    } finally {
      setIsLoading(false);
    }
  };

  const loadRoomDocuments = async () => {
    if (!roomId) return;

    try {
      setDocumentsLoading(true);
      const roomDocuments = await roomService.getRoomDocuments(roomId);
      setDocuments(roomDocuments);
    } catch (error: any) {
      console.error("Load room documents error:", error);
      toast.error(error.message || "Failed to load room documents");
    } finally {
      setDocumentsLoading(false);
    }
  };

  const handleRemoveDocument = async (roomDocumentId: string) => {
    if (!user) return;

    if (
      !confirm("Are you sure you want to remove this document from the room?")
    ) {
      return;
    }

    try {
      await roomService.removeDocumentFromRoom(roomDocumentId, user.id);
      toast.success("Document removed from room");
      loadRoomDocuments();
    } catch (error: any) {
      toast.error(error.message || "Failed to remove document");
    }
  };

  const handleLeaveRoom = async () => {
    if (!room || !user || room.user_role === "admin") return;

    const confirmLeave = window.confirm(
      `Are you sure you want to leave "${room.name}"? You will lose access to all shared documents in this room.`
    );

    if (!confirmLeave) return;

    try {
      setIsLeavingRoom(true);
      await roomService.leaveRoom(roomId!, user.id);
      toast.success("Successfully left the room");
      navigate("/rooms");
    } catch (error: any) {
      toast.error(error.message || "Failed to leave room");
    } finally {
      setIsLeavingRoom(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!room) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Room not found
          </h3>
          <p className="text-gray-600 mb-6">
            The room you're looking for doesn't exist or you don't have access
            to it.
          </p>
          <button onClick={() => navigate("/rooms")} className="btn-primary">
            Back to Rooms
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
              <div className="flex items-start space-x-3 sm:space-x-4 min-w-0 flex-1">
                <button
                  onClick={() => navigate("/rooms")}
                  className="text-gray-600 hover:text-gray-800 flex-shrink-0 mt-1"
                >
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <div className="min-w-0 flex-1">
                  <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 break-words">
                    {room.name}
                  </h1>
                  <p className="mt-1 text-sm sm:text-base text-gray-600 break-words">
                    {room.description || "No description provided"}
                  </p>
                </div>
              </div>

              {/* Mobile: Badges and Settings in a row */}
              <div className="flex items-center justify-between sm:justify-end space-x-2 sm:space-x-3 flex-shrink-0">
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs sm:text-sm font-medium rounded-full bg-gray-100 text-gray-800 whitespace-nowrap">
                    {room.room_code}
                  </span>
                  <span
                    className={`px-2 py-1 text-xs sm:text-sm font-medium rounded-full whitespace-nowrap ${
                      room.user_role === "admin"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {room.user_role}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {room.user_role === "admin" ? (
                    <>
                      {/* Invite Users Button */}
                      <button
                        onClick={() => setShowInviteUsers(true)}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors flex-shrink-0"
                        title="Invite Users"
                      >
                        <svg
                          className="w-4 h-4 sm:w-5 sm:h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                          />
                        </svg>
                      </button>

                      {/* Create Invite Link Button */}
                      <button
                        onClick={() => setShowCreateLink(true)}
                        className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors flex-shrink-0"
                        title="Create Invite Link"
                      >
                        <svg
                          className="w-4 h-4 sm:w-5 sm:h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                          />
                        </svg>
                      </button>

                      {/* Settings Button */}
                      <button
                        onClick={() => setShowSettings(true)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors flex-shrink-0"
                        title="Room Settings"
                      >
                        <svg
                          className="w-4 h-4 sm:w-5 sm:h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={handleLeaveRoom}
                      disabled={isLeavingRoom}
                      className="px-3 py-1.5 text-xs sm:text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 hover:border-red-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      title="Leave Room"
                    >
                      {isLeavingRoom ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600"></div>
                          <span className="hidden sm:inline">Leaving...</span>
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                            />
                          </svg>
                          <span className="hidden sm:inline">Leave</span>
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Members</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {room.member_count}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Documents</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {room.document_count}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Privacy</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {room.is_private ? "Private" : "Public"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-4 sm:space-x-8 overflow-x-auto">
              <button
                onClick={() => setSelectedTab("documents")}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  selectedTab === "documents"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Documents ({room.document_count})
              </button>
              <button
                onClick={() => setSelectedTab("members")}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  selectedTab === "members"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Members ({room.member_count})
              </button>
              <button
                onClick={() => setSelectedTab("activity")}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  selectedTab === "activity"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Activity
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {selectedTab === "documents" && (
            <RoomDocumentList
              documents={documents}
              isLoading={documentsLoading}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              onRemoveDocument={handleRemoveDocument}
              canManageDocuments={room.user_role === "admin"}
              currentUserId={user?.id || ""}
            />
          )}

          {selectedTab === "members" && (
            <div className="space-y-4">
              {room.members.map((member) => (
                <div key={member.id} className="card p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-sm font-medium text-gray-600">
                          {member.profiles?.full_name?.charAt(0) ||
                            member.profiles?.email.charAt(0)}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {member.profiles?.full_name || member.profiles?.email}
                        </h3>
                        <p className="text-sm text-gray-500 truncate">
                          {member.profiles?.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full text-center ${
                          member.role === "admin"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {member.role}
                      </span>
                      <span className="text-xs sm:text-sm text-gray-500 text-center sm:text-left">
                        Joined {formatDateTime(member.joined_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === "activity" && (
            <div className="card p-4 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Recent Activity
              </h3>
              <RoomActivityFeed roomId={roomId!} limit={20} />
            </div>
          )}
        </div>
      </div>

      {/* Room Settings Modal */}
      {showSettings && room && (
        <RoomSettings
          room={room}
          onClose={() => setShowSettings(false)}
          onRoomUpdate={(updatedRoom) => {
            setRoom({ ...room, ...updatedRoom });
            setShowSettings(false);
          }}
          onRefresh={loadRoomDetails}
          currentUserId={user?.id || ""}
        />
      )}

      {/* Invite Users Modal */}
      {showInviteUsers && room && (
        <InviteUsersModal
          room={room}
          onClose={() => setShowInviteUsers(false)}
          onInviteComplete={() => {
            setShowInviteUsers(false);
            // Optionally reload room members
            loadRoomDetails();
          }}
        />
      )}

      {/* Create Invite Link Modal */}
      {showCreateLink && room && (
        <CreateInviteLinkModal
          room={room}
          onClose={() => setShowCreateLink(false)}
          onLinkCreated={() => {
            setShowCreateLink(false);
          }}
        />
      )}
    </DashboardLayout>
  );
}
