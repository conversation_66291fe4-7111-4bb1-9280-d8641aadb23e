import {
  DocumentIcon,
  DocumentTextIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  PresentationChartBarIcon,
  TableCellsIcon,
  CodeBracketIcon,
} from "@heroicons/react/24/outline";

interface FileIconProps {
  fileType: string;
  mimeType?: string;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
}

const sizeClasses = {
  sm: "h-4 w-4",
  md: "h-5 w-5",
  lg: "h-6 w-6",
  xl: "h-8 w-8",
};

const fileTypeColors = {
  pdf: "text-red-500",
  word: "text-blue-500",
  excel: "text-green-500",
  powerpoint: "text-orange-500",
  image: "text-purple-500",
  video: "text-pink-500",
  audio: "text-indigo-500",
  archive: "text-yellow-600",
  code: "text-gray-600",
  text: "text-gray-500",
  default: "text-gray-400",
};

export function FileIcon({
  fileType,
  mimeType,
  className = "",
  size = "md",
}: FileIconProps) {
  const sizeClass = sizeClasses[size];

  const getIconAndColor = () => {
    const type = fileType.toLowerCase();
    const mime = mimeType?.toLowerCase() || "";

    // PDF files
    if (type.includes("pdf") || mime.includes("pdf")) {
      return { Icon: DocumentIcon, color: fileTypeColors.pdf };
    }

    // Word documents
    if (
      type.includes("doc") ||
      type.includes("word") ||
      mime.includes("word")
    ) {
      return { Icon: DocumentTextIcon, color: fileTypeColors.word };
    }

    // Excel spreadsheets
    if (
      type.includes("sheet") ||
      type.includes("excel") ||
      mime.includes("excel")
    ) {
      return { Icon: TableCellsIcon, color: fileTypeColors.excel };
    }

    // PowerPoint presentations
    if (
      type.includes("presentation") ||
      type.includes("powerpoint") ||
      mime.includes("presentation")
    ) {
      return {
        Icon: PresentationChartBarIcon,
        color: fileTypeColors.powerpoint,
      };
    }

    // Images
    if (type.includes("image") || mime.startsWith("image/")) {
      return { Icon: PhotoIcon, color: fileTypeColors.image };
    }

    // Videos
    if (type.includes("video") || mime.startsWith("video/")) {
      return { Icon: VideoCameraIcon, color: fileTypeColors.video };
    }

    // Audio files
    if (type.includes("audio") || mime.startsWith("audio/")) {
      return { Icon: MusicalNoteIcon, color: fileTypeColors.audio };
    }

    // Archive files
    if (
      type.includes("zip") ||
      type.includes("rar") ||
      type.includes("archive")
    ) {
      return { Icon: ArchiveBoxIcon, color: fileTypeColors.archive };
    }

    // Code files
    if (
      type.includes("code") ||
      mime.includes("javascript") ||
      mime.includes("json") ||
      mime.includes("xml") ||
      mime.includes("html") ||
      mime.includes("css")
    ) {
      return { Icon: CodeBracketIcon, color: fileTypeColors.code };
    }

    // Text files
    if (type.includes("text") || mime.includes("text/")) {
      return { Icon: DocumentTextIcon, color: fileTypeColors.text };
    }

    // Default
    return { Icon: DocumentIcon, color: fileTypeColors.default };
  };

  const { Icon, color } = getIconAndColor();

  return <Icon className={`${sizeClass} ${color} ${className}`} />;
}

// Modern Windows 11-style folder icon with better styling
interface FolderIconProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  isOpen?: boolean;
  hasChildren?: boolean;
}

export function EnhancedFolderIcon({
  className = "",
  size = "md",
  isOpen = false,
  hasChildren = false,
}: FolderIconProps) {
  const sizeMap = {
    sm: { width: 16, height: 16, fontSize: "text-xs" },
    md: { width: 20, height: 20, fontSize: "text-sm" },
    lg: { width: 24, height: 24, fontSize: "text-base" },
    xl: { width: 32, height: 32, fontSize: "text-lg" },
  };

  const { width, height } = sizeMap[size];

  return (
    <div className={`relative ${className}`}>
      <svg
        width={width}
        height={height}
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="drop-shadow-sm"
      >
        {/* Folder shadow */}
        <path
          d="M4 8.5C4 7.11929 5.11929 6 6.5 6H12.5858C13.1162 6 13.6249 6.21071 14 6.58579L16.4142 9H26.5C27.8807 9 29 10.1193 29 11.5V24.5C29 25.8807 27.8807 27 26.5 27H6.5C5.11929 27 4 25.8807 4 24.5V8.5Z"
          fill="rgba(0, 0, 0, 0.1)"
          transform="translate(1, 1)"
        />

        {/* Main folder body */}
        <path
          d="M4 8.5C4 7.11929 5.11929 6 6.5 6H12.5858C13.1162 6 13.6249 6.21071 14 6.58579L16.4142 9H26.5C27.8807 9 29 10.1193 29 11.5V24.5C29 25.8807 27.8807 27 26.5 27H6.5C5.11929 27 4 25.8807 4 24.5V8.5Z"
          fill={isOpen ? "#4F9CF9" : "#5B9BD5"}
        />

        {/* Folder tab */}
        <path
          d="M4 8.5C4 7.11929 5.11929 6 6.5 6H12.5858C13.1162 6 13.6249 6.21071 14 6.58579L16.4142 9H26.5C27.8807 9 29 10.1193 29 11.5V12H4V8.5Z"
          fill={isOpen ? "#6BB6FF" : "#7BB3E0"}
        />

        {/* Folder highlight */}
        <path
          d="M4 8.5C4 7.11929 5.11929 6 6.5 6H12.5858C13.1162 6 13.6249 6.21071 14 6.58579L16.4142 9H26.5C27.8807 9 29 10.1193 29 11.5V13H4V8.5Z"
          fill="url(#folderGradient)"
          opacity="0.3"
        />

        {/* Gradient definition */}
        <defs>
          <linearGradient id="folderGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="white" stopOpacity="0.4" />
            <stop offset="100%" stopColor="white" stopOpacity="0" />
          </linearGradient>
        </defs>
      </svg>

      {/* Children indicator */}
      {hasChildren && (
        <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full border border-white shadow-sm" />
      )}
    </div>
  );
}

// Utility function to get file type from filename
export function getFileTypeFromName(filename: string): string {
  const extension = filename.split(".").pop()?.toLowerCase() || "";

  const typeMap: Record<string, string> = {
    // Documents
    pdf: "PDF Document",
    doc: "Word Document",
    docx: "Word Document",

    // Spreadsheets
    xls: "Excel Spreadsheet",
    xlsx: "Excel Spreadsheet",
    csv: "CSV File",

    // Presentations
    ppt: "PowerPoint Presentation",
    pptx: "PowerPoint Presentation",

    // Images
    jpg: "JPEG Image",
    jpeg: "JPEG Image",
    png: "PNG Image",
    gif: "GIF Image",
    svg: "SVG Image",
    webp: "WebP Image",

    // Videos
    mp4: "MP4 Video",
    avi: "AVI Video",
    mov: "QuickTime Video",
    wmv: "WMV Video",

    // Audio
    mp3: "MP3 Audio",
    wav: "WAV Audio",
    flac: "FLAC Audio",

    // Archives
    zip: "ZIP Archive",
    rar: "RAR Archive",
    "7z": "7-Zip Archive",
    tar: "TAR Archive",

    // Text
    txt: "Text File",
    rtf: "Rich Text File",

    // Code
    js: "JavaScript File",
    ts: "TypeScript File",
    html: "HTML File",
    css: "CSS File",
    json: "JSON File",
    xml: "XML File",
  };

  return typeMap[extension] || "Unknown File";
}

// Component for displaying file type badge
interface FileTypeBadgeProps {
  fileType: string;
  className?: string;
}

export function FileTypeBadge({
  fileType,
  className = "",
}: FileTypeBadgeProps) {
  const type = fileType.toLowerCase();

  const getBadgeColor = () => {
    if (type.includes("pdf")) return "bg-red-100 text-red-800";
    if (type.includes("word") || type.includes("doc"))
      return "bg-blue-100 text-blue-800";
    if (type.includes("excel") || type.includes("sheet"))
      return "bg-green-100 text-green-800";
    if (type.includes("powerpoint") || type.includes("presentation"))
      return "bg-orange-100 text-orange-800";
    if (type.includes("image")) return "bg-purple-100 text-purple-800";
    if (type.includes("video")) return "bg-pink-100 text-pink-800";
    if (type.includes("audio")) return "bg-indigo-100 text-indigo-800";
    if (type.includes("archive") || type.includes("zip"))
      return "bg-yellow-100 text-yellow-800";
    if (type.includes("text")) return "bg-gray-100 text-gray-800";
    return "bg-gray-100 text-gray-600";
  };

  const getShortType = () => {
    if (type.includes("pdf")) return "PDF";
    if (type.includes("word") || type.includes("doc")) return "DOC";
    if (type.includes("excel") || type.includes("sheet")) return "XLS";
    if (type.includes("powerpoint") || type.includes("presentation"))
      return "PPT";
    if (type.includes("image")) return "IMG";
    if (type.includes("video")) return "VID";
    if (type.includes("audio")) return "AUD";
    if (type.includes("archive") || type.includes("zip")) return "ZIP";
    if (type.includes("text")) return "TXT";
    return "FILE";
  };

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getBadgeColor()} ${className}`}
    >
      {getShortType()}
    </span>
  );
}
