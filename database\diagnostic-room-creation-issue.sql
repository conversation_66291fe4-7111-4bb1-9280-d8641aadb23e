-- Comprehensive Diagnostic Script for Room Creation Issue
-- This script investigates the database schema and triggers to identify the source of the 'NEW.user_id' error

-- ============================================================================
-- PART 1: SCHEMA INVESTIGATION
-- ============================================================================

-- Check the rooms table structure
SELECT 
    'ROOMS TABLE STRUCTURE' as investigation_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'rooms' 
ORDER BY ordinal_position;

-- Check if there are any columns with 'user_id' in the rooms table
SELECT 
    'ROOMS TABLE USER_ID COLUMNS' as investigation_type,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND column_name LIKE '%user_id%';

-- ============================================================================
-- PART 2: TRIGGER INVESTIGATION
-- ============================================================================

-- List all triggers on the rooms table
SELECT 
    'ROOMS TABLE TRIGGERS' as investigation_type,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'rooms';

-- Get detailed trigger information
SELECT 
    'TRIGGER DETAILS' as investigation_type,
    t.trigger_name,
    t.action_timing,
    t.event_manipulation,
    t.action_statement,
    p.proname as function_name,
    p.prosrc as function_source
FROM information_schema.triggers t
LEFT JOIN pg_proc p ON p.proname = REPLACE(SPLIT_PART(t.action_statement, '(', 1), 'EXECUTE FUNCTION ', '')
WHERE t.event_object_table = 'rooms';

-- ============================================================================
-- PART 3: FUNCTION INVESTIGATION
-- ============================================================================

-- Find all functions that might be related to room creation
SELECT 
    'ROOM RELATED FUNCTIONS' as investigation_type,
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE proname LIKE '%room%' 
OR prosrc LIKE '%room%'
OR prosrc LIKE '%NEW.user_id%';

-- Find functions that reference NEW.user_id (the problematic pattern)
SELECT 
    'FUNCTIONS WITH NEW.USER_ID' as investigation_type,
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE prosrc LIKE '%NEW.user_id%';

-- Find functions that reference NEW.created_by (the correct pattern)
SELECT 
    'FUNCTIONS WITH NEW.CREATED_BY' as investigation_type,
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE prosrc LIKE '%NEW.created_by%';

-- ============================================================================
-- PART 4: RLS POLICIES INVESTIGATION
-- ============================================================================

-- Check RLS policies on rooms table
SELECT 
    'ROOMS RLS POLICIES' as investigation_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'rooms';

-- ============================================================================
-- PART 5: DEPENDENCY INVESTIGATION
-- ============================================================================

-- Check for any dependencies that might affect room creation
SELECT 
    'FUNCTION DEPENDENCIES' as investigation_type,
    p.proname as function_name,
    d.refobjid::regclass as depends_on
FROM pg_proc p
JOIN pg_depend d ON d.objid = p.oid
WHERE p.proname LIKE '%room%'
AND d.deptype = 'n';

-- ============================================================================
-- PART 6: RECENT CHANGES INVESTIGATION
-- ============================================================================

-- Check for any recent functions or triggers (if audit info is available)
SELECT 
    'RECENT FUNCTIONS' as investigation_type,
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE proname LIKE '%add%room%' 
OR proname LIKE '%room%creator%'
OR proname LIKE '%room%admin%';

-- ============================================================================
-- PART 7: VALIDATION QUERIES
-- ============================================================================

-- Validate that room_members table exists and has correct structure
SELECT 
    'ROOM_MEMBERS TABLE STRUCTURE' as investigation_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'room_members' 
ORDER BY ordinal_position;

-- Check for any constraints that might affect the trigger
SELECT 
    'ROOMS TABLE CONSTRAINTS' as investigation_type,
    constraint_name,
    constraint_type,
    column_name
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_name = 'rooms';

-- ============================================================================
-- SUMMARY QUERY
-- ============================================================================

-- Final summary to help identify the issue
DO $$
DECLARE
    trigger_count INTEGER;
    function_count INTEGER;
    problematic_functions INTEGER;
BEGIN
    -- Count triggers on rooms table
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE event_object_table = 'rooms';
    
    -- Count room-related functions
    SELECT COUNT(*) INTO function_count
    FROM pg_proc 
    WHERE proname LIKE '%room%';
    
    -- Count functions with problematic NEW.user_id reference
    SELECT COUNT(*) INTO problematic_functions
    FROM pg_proc 
    WHERE prosrc LIKE '%NEW.user_id%';
    
    RAISE NOTICE '=== DIAGNOSTIC SUMMARY ===';
    RAISE NOTICE 'Triggers on rooms table: %', trigger_count;
    RAISE NOTICE 'Room-related functions: %', function_count;
    RAISE NOTICE 'Functions with NEW.user_id: %', problematic_functions;
    
    IF problematic_functions > 0 THEN
        RAISE WARNING 'Found % function(s) with problematic NEW.user_id reference!', problematic_functions;
    ELSE
        RAISE NOTICE 'No functions found with NEW.user_id reference';
    END IF;
END $$;
