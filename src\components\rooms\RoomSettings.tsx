import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import toast from "react-hot-toast";
import { roomService } from "../../lib/roomService";
import type { RoomWithDetails, RoomMember } from "../../lib/roomService";

const roomUpdateSchema = z.object({
  name: z.string().min(1, "Room name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  is_private: z.boolean(),
  max_members: z
    .number()
    .min(2, "Minimum 2 members")
    .max(1000, "Maximum 1000 members"),
});

type RoomUpdateFormData = z.infer<typeof roomUpdateSchema>;

interface RoomSettingsProps {
  room: RoomWithDetails & { members: RoomMember[] };
  onClose: () => void;
  onRoomUpdate: (room: RoomWithDetails) => void;
  onRefresh?: () => void;
  currentUserId: string;
}

export function RoomSettings({
  room,
  onClose,
  onRoomUpdate,
  onRefresh,
  currentUserId,
}: RoomSettingsProps) {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"general" | "members" | "danger">(
    "general"
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RoomUpdateFormData>({
    defaultValues: {
      name: room.name,
      description: room.description || "",
      is_private: room.is_private,
      max_members: room.max_members,
    },
  });

  const onSubmit = async (data: RoomUpdateFormData) => {
    try {
      setIsLoading(true);
      await roomService.updateRoom(room.id, data, currentUserId);

      // Update the room object with new data
      const updatedRoom = { ...room, ...data };
      onRoomUpdate(updatedRoom);

      toast.success("Room settings updated successfully!");
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to update room settings");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string, memberName: string) => {
    if (
      !confirm(`Are you sure you want to remove ${memberName} from the room?`)
    ) {
      return;
    }

    try {
      await roomService.removeMemberFromRoom(room.id, memberId, currentUserId);
      toast.success("Member removed successfully");
      // Refresh room data
      onRefresh?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to remove member");
    }
  };

  const handleDeleteRoom = async () => {
    const confirmText = `DELETE ${room.name}`;
    const userInput = prompt(
      `This action cannot be undone. Type "${confirmText}" to confirm deletion:`
    );

    if (userInput !== confirmText) {
      toast.error("Room deletion cancelled - text didn't match");
      return;
    }

    try {
      setIsLoading(true);
      await roomService.deleteRoom(room.id, currentUserId);
      toast.success("Room deleted successfully");
      navigate("/rooms");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete room");
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Room Settings</h3>
          <button
            onClick={onClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {["general", "members", "danger"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === "general" && (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Room Name
                </label>
                <input
                  type="text"
                  {...register("name")}
                  disabled={isLoading}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.name.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register("description")}
                  disabled={isLoading}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                  placeholder="Optional room description..."
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Members
                </label>
                <input
                  type="number"
                  {...register("max_members", { valueAsNumber: true })}
                  disabled={isLoading}
                  min="2"
                  max="1000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                />
                {errors.max_members && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.max_members.message}
                  </p>
                )}
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register("is_private")}
                    disabled={isLoading}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                  />
                  <span className="text-sm text-gray-700">
                    Private room (only visible to members)
                  </span>
                </label>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? "Saving..." : "Save Changes"}
                </button>
              </div>
            </form>
          )}

          {activeTab === "members" && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-medium text-gray-900">
                  Room Members
                </h4>
                <span className="text-sm text-gray-500">
                  {room.member_count} / {room.max_members} members
                </span>
              </div>

              {/* All Members (including creator) */}
              {room.members.map((member) => {
                const isCreator = member.user_id === room.created_by;
                return (
                  <div
                    key={member.id}
                    className={`card p-4 ${
                      isCreator ? "bg-blue-50 border-blue-200" : ""
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            isCreator ? "bg-blue-100" : "bg-gray-200"
                          }`}
                        >
                          {isCreator ? (
                            <svg
                              className="w-4 h-4 text-blue-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                              />
                            </svg>
                          ) : (
                            <span className="text-xs font-medium text-gray-600">
                              {member.profiles?.full_name?.charAt(0) ||
                                member.profiles?.email.charAt(0)}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {member.profiles?.full_name ||
                              member.profiles?.email}
                            {isCreator && (
                              <span className="ml-2 text-xs text-blue-600">
                                (Creator)
                              </span>
                            )}
                          </p>
                          <p className="text-xs text-gray-500">
                            {member.profiles?.email}
                            {isCreator && (
                              <span className="ml-2">• Cannot be removed</span>
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                            member.role === "admin"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {member.role}
                        </span>
                        {!isCreator && (
                          <button
                            onClick={() =>
                              handleRemoveMember(
                                member.user_id,
                                member.profiles?.full_name ||
                                  member.profiles?.email ||
                                  "Unknown"
                              )
                            }
                            className="text-red-600 hover:text-red-800 text-xs p-1"
                            title="Remove member"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {activeTab === "danger" && (
            <div className="space-y-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="text-lg font-medium text-red-900 mb-2">
                  Danger Zone
                </h4>
                <p className="text-sm text-red-700 mb-4">
                  These actions are irreversible. Please proceed with caution.
                </p>

                <button
                  onClick={handleDeleteRoom}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  {isLoading ? "Deleting..." : "Delete Room"}
                </button>

                <p className="text-xs text-red-600 mt-2">
                  This will permanently delete the room, all shared documents
                  will be unshared, and all members will be removed.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
