# Debug Sharing Issues

## Current Issues Fixed

### 1. ✅ Query Syntax Error (406 Not Acceptable)
**Problem**: Malformed query with `.or()` and `.eq()` in wrong order
**Fix**: Reordered query methods and used `.maybeSingle()` instead of `.single()`

### 2. 🔧 Storage Copy Error (400 Bad Request)
**Problem**: Storage copy operation failing
**Fix**: Added fallback download/upload method with better error logging

## Debug Steps

### 1. Check Console Logs
When you try to share a document, look for these logs:
- "Starting share process:" - Shows the IDs being used
- "Document found:" - Confirms document exists
- "Copying file:" - Shows file paths being used
- Any error messages with detailed information

### 2. Common Storage Issues

**File Path Problems**:
- Original path might be malformed
- User folder might not exist
- File might not exist in storage

**Permission Issues**:
- RLS policies might block storage access
- User might not have storage permissions

**Storage Bucket Issues**:
- Bucket might not exist
- Bucket policies might be restrictive

### 3. Manual Test in Supabase

Try this query in your Supabase SQL editor to check storage:
```sql
-- Check if documents table has valid file paths
SELECT id, title, file_path, user_id 
FROM documents 
WHERE user_id = 'YOUR_USER_ID' 
LIMIT 5;
```

### 4. Storage Bucket Check

In Supabase Dashboard:
1. Go to Storage
2. Check if "documents" bucket exists
3. Check bucket policies
4. Verify files exist at the paths shown in documents table

### 5. Alternative Sharing Method

If storage copy continues to fail, we can implement a simpler approach:
1. Create document record without file copy
2. Use symbolic links or references
3. Copy file on first access

## Next Steps

1. **Try sharing again** and check console logs
2. **Report the exact error messages** from console
3. **Check Supabase storage dashboard** for file existence
4. **Verify RLS policies** are not blocking operations

## Temporary Workaround

If sharing continues to fail, we can temporarily disable permanent copying:
1. Create share records without copying files
2. Use reference-based sharing until storage issues are resolved
3. Migrate to permanent sharing once storage is working

## Expected Console Output

When sharing works correctly, you should see:
```
Starting share process: {documentId: "...", sharedWith: "...", sharedBy: "..."}
Document found: Document Title
Copying file: {originalPath: "user1/file.pdf", newFilePath: "user2/timestamp_copy.pdf"}
Share created successfully
```

If you see different output or errors, please share the exact console messages.
