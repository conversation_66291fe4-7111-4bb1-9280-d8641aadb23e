import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { CreateRoomModal } from "../components/rooms/CreateRoomModal";
import { JoinRoomModal } from "../components/rooms/JoinRoomModal";
import { roomService } from "../lib/roomService";
import type { RoomWithDetails } from "../lib/roomService";
import { useAuth } from "../contexts/AuthContext";
import toast from "react-hot-toast";
import { formatDateTime } from "../lib/utils";
import { usePageTitle } from "../hooks/usePageTitle";

export function Rooms() {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Set page title
  usePageTitle("Rooms");

  const [rooms, setRooms] = useState<RoomWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [showJoinRoom, setShowJoinRoom] = useState(false);

  useEffect(() => {
    loadRooms();
  }, []);

  const loadRooms = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userRooms = await roomService.getUserRooms(user.id);
      setRooms(userRooms);
    } catch (error: any) {
      console.error("Load rooms error:", error);
      if (
        error.message?.includes("parse logic tree") ||
        error.message?.includes("policy")
      ) {
        toast.error(
          "Database query issue. Please try again or contact support."
        );
      } else {
        toast.error(error.message || "Failed to load rooms");
      }
      setRooms([]); // Set empty array to prevent crashes
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoomCreated = () => {
    loadRooms(); // Reload rooms after creating
  };

  const handleRoomJoined = () => {
    loadRooms(); // Reload rooms after joining
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading rooms...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  My Rooms
                </h1>
                <p className="mt-1 text-sm sm:text-base text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                  Collaborate and share documents with your classmates and
                  colleagues
                </p>
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-shrink-0">
                <button
                  onClick={() => setShowJoinRoom(true)}
                  className="btn-secondary flex items-center justify-center space-x-2 w-full sm:w-auto"
                >
                  <svg
                    className="w-4 h-4 sm:w-5 sm:h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  <span className="text-sm sm:text-base">Join Room</span>
                </button>
                <button
                  onClick={() => setShowCreateRoom(true)}
                  className="btn-primary flex items-center justify-center space-x-2 w-full sm:w-auto"
                >
                  <svg
                    className="w-4 h-4 sm:w-5 sm:h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  <span className="text-sm sm:text-base">Create Room</span>
                </button>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Rooms
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {rooms.length}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Rooms Created
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {rooms.filter((room) => room.user_role === "admin").length}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Shared Documents
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {rooms.reduce(
                      (sum, room) => sum + (room.document_count || 0),
                      0
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Rooms Grid */}
          {rooms.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
                No rooms yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
                Create your first room or join an existing one to start
                collaborating.
              </p>
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => setShowCreateRoom(true)}
                  className="btn-primary"
                >
                  Create Room
                </button>
                <button
                  onClick={() => setShowJoinRoom(true)}
                  className="btn-secondary"
                >
                  Join Room
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rooms.map((room) => (
                <div
                  key={room.id}
                  className="card p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {room.name}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {room.description || "No description provided"}
                      </p>
                    </div>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        room.user_role === "admin"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {room.user_role}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Room Code</span>
                      <span className="font-mono font-medium bg-gray-100 px-2 py-1 rounded">
                        {room.room_code}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="block text-xs text-gray-500">
                          Members
                        </span>
                        <span className="font-medium">{room.member_count}</span>
                      </div>
                      <div>
                        <span className="block text-xs text-gray-500">
                          Documents
                        </span>
                        <span className="font-medium">
                          {room.document_count}
                        </span>
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Created {formatDateTime(room.created_at)}
                    </div>

                    {room.is_private && (
                      <div className="flex items-center text-xs text-amber-600">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                          />
                        </svg>
                        Private Room
                      </div>
                    )}
                  </div>

                  <div className="mt-6 space-y-3">
                    {/* Quick Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                      <span>
                        Last activity: {formatDateTime(room.updated_at)}
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full transition-theme duration-theme ${
                          room.is_private
                            ? "bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300"
                            : "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                        }`}
                      >
                        {room.is_private ? "Private" : "Public"}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => navigate(`/rooms/${room.id}`)}
                        className="flex-1 mr-2 bg-blue-600 text-white text-sm font-medium py-2 px-3 rounded-md hover:bg-blue-700 transition-colors"
                      >
                        View Room
                      </button>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() =>
                            navigate(`/rooms/${room.id}?tab=documents`)
                          }
                          className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                          title="View Documents"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </button>
                        <button
                          onClick={() =>
                            navigate(`/documents?shareToRoom=${room.id}`)
                          }
                          className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
                          title="Share Documents"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                            />
                          </svg>
                        </button>
                        {room.user_role === "admin" && (
                          <button
                            onClick={() =>
                              navigate(`/rooms/${room.id}?tab=settings`)
                            }
                            className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
                            title="Room Settings"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Room Modal */}
      {showCreateRoom && user && (
        <CreateRoomModal
          onClose={() => setShowCreateRoom(false)}
          onRoomCreated={handleRoomCreated}
          userId={user.id}
        />
      )}

      {/* Join Room Modal */}
      {showJoinRoom && user && (
        <JoinRoomModal
          onClose={() => setShowJoinRoom(false)}
          onRoomJoined={handleRoomJoined}
          userId={user.id}
        />
      )}
    </DashboardLayout>
  );
}
