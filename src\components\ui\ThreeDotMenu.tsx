import React, { useState, useRef, useEffect } from "react";
import { EllipsisVerticalIcon } from "@heroicons/react/24/outline";
import type { ContextMenuItem } from "./ContextMenu";
import { useMobileActionModal, MobileActionModal } from "./MobileActionModal";

interface ThreeDotMenuProps {
  title: string;
  items: ContextMenuItem[];
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function ThreeDotMenu({
  title,
  items,
  className = "",
  size = "md",
}: ThreeDotMenuProps) {
  const mobileModal = useMobileActionModal();
  const [isDesktopDropdownOpen, setIsDesktopDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const isMobile = useIsMobile();

  const sizeClasses = {
    sm: "p-1.5",
    md: "p-2",
    lg: "p-3",
  };

  const iconSizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDesktopDropdownOpen(false);
      }
    };

    if (isDesktopDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDesktopDropdownOpen]);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isMobile) {
      mobileModal.openModal(title, items);
    } else {
      setIsDesktopDropdownOpen(!isDesktopDropdownOpen);
    }
  };

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`
          ${sizeClasses[size]}
          ${className}
          rounded-full
          hover:bg-gray-100
          active:bg-gray-200
          transition-colors
          duration-200
          min-h-[44px]
          min-w-[44px]
          flex
          items-center
          justify-center
          touch-manipulation
          ${isDesktopDropdownOpen && !isMobile ? "bg-gray-100" : ""}
        `}
        aria-label="More actions"
      >
        <EllipsisVerticalIcon
          className={`${iconSizeClasses[size]} text-gray-500`}
        />
      </button>

      {/* Desktop Dropdown */}
      {!isMobile && isDesktopDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 top-full mt-1 z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-48"
        >
          {items.map((item, index) => (
            <React.Fragment key={item.id}>
              {item.divider && index > 0 && (
                <div className="border-t border-gray-100 my-1" />
              )}
              <button
                onClick={() => {
                  if (!item.disabled) {
                    item.onClick();
                    setIsDesktopDropdownOpen(false);
                  }
                }}
                disabled={item.disabled}
                className={`w-full flex items-center px-3 py-2 text-sm text-left transition-colors ${
                  item.disabled
                    ? "text-gray-400 cursor-not-allowed"
                    : item.destructive
                    ? "text-red-600 hover:bg-red-50"
                    : "text-gray-700 hover:bg-gray-50"
                }`}
              >
                <item.icon className="h-4 w-4 mr-3 flex-shrink-0" />
                {item.label}
              </button>
            </React.Fragment>
          ))}
        </div>
      )}

      {/* Mobile Modal */}
      <MobileActionModal
        isOpen={mobileModal.isOpen}
        onClose={mobileModal.closeModal}
        title={mobileModal.title}
        items={mobileModal.items}
      />
    </div>
  );
}

// Hook to detect if device is mobile/touch
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkIsMobile = () => {
      // Check for touch capability and screen size
      const hasTouchScreen =
        "ontouchstart" in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth < 1024; // lg breakpoint
      setIsMobile(hasTouchScreen && isSmallScreen);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  return isMobile;
}

// Enhanced touch event handlers
export function useTouchHandlers() {
  const [touchStart, setTouchStart] = React.useState<{
    x: number;
    y: number;
    time: number;
  } | null>(null);
  const [isLongPress, setIsLongPress] = React.useState(false);
  const longPressTimer = React.useRef<NodeJS.Timeout | null>(null);

  const handleTouchStart = React.useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchStart({
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    });
    setIsLongPress(false);

    // Start long press timer
    longPressTimer.current = setTimeout(() => {
      setIsLongPress(true);
    }, 500); // 500ms for long press
  }, []);

  const handleTouchMove = React.useCallback(
    (e: React.TouchEvent) => {
      if (!touchStart || !longPressTimer.current) return;

      const touch = e.touches[0];
      const deltaX = Math.abs(touch.clientX - touchStart.x);
      const deltaY = Math.abs(touch.clientY - touchStart.y);

      // If moved too much, cancel long press
      if (deltaX > 10 || deltaY > 10) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    },
    [touchStart]
  );

  const handleTouchEnd = React.useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
    setTouchStart(null);
  }, []);

  const handleTouchCancel = React.useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
    setTouchStart(null);
    setIsLongPress(false);
  }, []);

  React.useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, []);

  return {
    isLongPress,
    touchHandlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
      onTouchCancel: handleTouchCancel,
    },
  };
}
