-- SmartBagPack Admin Authentication Cleanup
-- Run this SQL in your Supabase SQL Editor to completely clean and fix the admin setup

-- Drop ALL existing admin-related policies (including the ones causing errors)
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update user roles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all documents" ON documents;
DROP POLICY IF EXISTS "Ad<PERSON> can delete documents" ON documents;
DROP POLICY IF EXISTS "Ad<PERSON> can view all rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can manage rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can delete rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can view all room members" ON room_members;
DROP POLICY IF EXISTS "Ad<PERSON> can manage room members" ON room_members;
DROP POLICY IF EXISTS "Ad<PERSON> can view all folders" ON folders;
DROP POLICY IF EXISTS "Ad<PERSON> can view all document shares" ON document_shares;
DROP POLICY IF EXISTS "Ad<PERSON> can view all room documents" ON room_documents;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all notifications" ON notifications;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all invitations" ON room_invitations;
DROP POLICY IF EXISTS "Admins can view all invitation links" ON room_invitation_links;
DROP POLICY IF EXISTS "Admins can view all documents in storage" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete documents in storage" ON storage.objects;

-- Drop JWT-based policies
DROP POLICY IF EXISTS "Admins can view all profiles via JWT" ON profiles;
DROP POLICY IF EXISTS "Admins can update user roles via JWT" ON profiles;
DROP POLICY IF EXISTS "Admins can view all documents via JWT" ON documents;
DROP POLICY IF EXISTS "Admins can delete documents via JWT" ON documents;
DROP POLICY IF EXISTS "Admins can view all rooms via JWT" ON rooms;
DROP POLICY IF EXISTS "Admins can manage rooms via JWT" ON rooms;
DROP POLICY IF EXISTS "Admins can delete rooms via JWT" ON rooms;
DROP POLICY IF EXISTS "Admins can view all room members via JWT" ON room_members;
DROP POLICY IF EXISTS "Admins can manage room members via JWT" ON room_members;
DROP POLICY IF EXISTS "Admins can view all folders via JWT" ON folders;
DROP POLICY IF EXISTS "Admins can view all document shares via JWT" ON document_shares;
DROP POLICY IF EXISTS "Admins can view all room documents via JWT" ON room_documents;
DROP POLICY IF EXISTS "Admins can view all notifications via JWT" ON notifications;
DROP POLICY IF EXISTS "Admins can view all invitations via JWT" ON room_invitations;
DROP POLICY IF EXISTS "Admins can view all invitation links via JWT" ON room_invitation_links;
DROP POLICY IF EXISTS "Admins can view all documents in storage via JWT" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete documents in storage via JWT" ON storage.objects;

-- Drop existing admin_actions policies
DROP POLICY IF EXISTS "Admins can insert admin actions" ON admin_actions;
DROP POLICY IF EXISTS "Admins can view admin actions" ON admin_actions;

-- Drop existing activity_logs policies
DROP POLICY IF EXISTS "System can insert activity logs" ON activity_logs;
DROP POLICY IF EXISTS "Admins can view all activity logs" ON activity_logs;
DROP POLICY IF EXISTS "Users can view own activity logs" ON activity_logs;

-- Drop existing functions
DROP FUNCTION IF EXISTS is_admin();
DROP FUNCTION IF EXISTS log_admin_action(TEXT, TEXT, UUID, JSONB);
DROP FUNCTION IF EXISTS log_user_activity(UUID, TEXT, TEXT, UUID, JSONB, INET, TEXT);

-- Create admin_actions table (if not exists)
CREATE TABLE IF NOT EXISTS admin_actions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  target_type TEXT CHECK (target_type IN ('user', 'document', 'room', 'system')) NOT NULL,
  target_id UUID,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activity_logs table (if not exists)
CREATE TABLE IF NOT EXISTS activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  target_type TEXT,
  target_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_id ON admin_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_created_at ON admin_actions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_actions_action_type ON admin_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_actions_target ON admin_actions(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_action_type ON activity_logs(action_type);

-- Enable RLS
ALTER TABLE admin_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Create safe admin check function
CREATE OR REPLACE FUNCTION is_admin() RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create admin action logging function
CREATE OR REPLACE FUNCTION log_admin_action(
  p_action_type TEXT,
  p_target_type TEXT,
  p_target_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  action_id UUID;
BEGIN
  IF NOT is_admin() THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;
  
  INSERT INTO admin_actions (admin_id, action_type, target_type, target_id, details)
  VALUES (auth.uid(), p_action_type, p_target_type, p_target_id, p_details)
  RETURNING id INTO action_id;
  
  RETURN action_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create user activity logging function
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id UUID,
  p_action_type TEXT,
  p_target_type TEXT DEFAULT NULL,
  p_target_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO activity_logs (user_id, action_type, target_type, target_id, details, ip_address, user_agent)
  VALUES (p_user_id, p_action_type, p_target_type, p_target_id, p_details, p_ip_address, p_user_agent)
  RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create NEW safe RLS policies
CREATE POLICY "admin_actions_insert_policy" ON admin_actions FOR INSERT WITH CHECK (
  auth.uid() = admin_id AND is_admin()
);

CREATE POLICY "admin_actions_select_policy" ON admin_actions FOR SELECT USING (
  is_admin()
);

CREATE POLICY "activity_logs_insert_policy" ON activity_logs FOR INSERT WITH CHECK (true);

CREATE POLICY "activity_logs_admin_select_policy" ON activity_logs FOR SELECT USING (
  is_admin()
);

CREATE POLICY "activity_logs_user_select_policy" ON activity_logs FOR SELECT USING (
  auth.uid() = user_id
);

-- Ensure admin user exists with proper setup
DO $$
DECLARE
  admin_user_id UUID;
BEGIN
  -- Get or create admin user
  SELECT id INTO admin_user_id FROM auth.users WHERE email = '<EMAIL>';
  
  IF admin_user_id IS NULL THEN
    -- Create new admin user
    INSERT INTO auth.users (
      instance_id,
      id,
      aud,
      role,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at,
      raw_app_meta_data,
      raw_user_meta_data,
      is_super_admin,
      confirmation_token,
      email_change,
      email_change_token_new,
      recovery_token
    ) VALUES (
      '00000000-0000-0000-0000-000000000000',
      gen_random_uuid(),
      'authenticated',
      'authenticated',
      '<EMAIL>',
      crypt('admin1234', gen_salt('bf')),
      NOW(),
      NOW(),
      NOW(),
      '{"provider": "email", "providers": ["email"]}',
      '{"full_name": "System Administrator", "role": "admin"}',
      false,
      '',
      '',
      '',
      ''
    ) RETURNING id INTO admin_user_id;
  ELSE
    -- Update existing admin user
    UPDATE auth.users 
    SET raw_user_meta_data = jsonb_set(
      jsonb_set(
        COALESCE(raw_user_meta_data, '{}'),
        '{role}',
        '"admin"'
      ),
      '{full_name}',
      '"System Administrator"'
    )
    WHERE id = admin_user_id;
  END IF;

  -- Ensure admin profile exists
  INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
  VALUES (
    admin_user_id,
    '<EMAIL>',
    'System Administrator',
    'admin',
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    role = 'admin',
    full_name = COALESCE(profiles.full_name, 'System Administrator'),
    updated_at = NOW();
END $$;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION log_admin_action(TEXT, TEXT, UUID, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION log_user_activity(UUID, TEXT, TEXT, UUID, JSONB, INET, TEXT) TO authenticated;

-- Success message
SELECT 'Admin authentication setup completed successfully!' as result;
