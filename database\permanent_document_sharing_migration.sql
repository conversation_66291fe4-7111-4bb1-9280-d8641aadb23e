-- Permanent Document Sharing Migration
-- This migration implements true document ownership transfer

-- Step 1: Update document_shares table for permanent sharing tracking
ALTER TABLE document_shares 
ADD COLUMN IF NOT EXISTS is_permanent BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS original_owner_id UUID REFERENCES auth.users(id);

-- Step 2: Add metadata to documents table for tracking shared documents
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS is_shared_copy BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS original_document_id UUID REFERENCES documents(id),
ADD COLUMN IF NOT EXISTS shared_from_user_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS shared_at TIMESTAMP WITH TIME ZONE;

-- Step 3: Create index for performance
CREATE INDEX IF NOT EXISTS idx_documents_shared_copy ON documents(is_shared_copy);
CREATE INDEX IF NOT EXISTS idx_documents_original_document_id ON documents(original_document_id);
CREATE INDEX IF NOT EXISTS idx_document_shares_is_permanent ON document_shares(is_permanent);

-- Step 4: Update RLS policies for permanent sharing
DROP POLICY IF EXISTS "Users can view shared documents" ON documents;

CREATE POLICY "Users can view shared documents" ON documents FOR SELECT USING (
  -- Own documents (including permanently shared copies)
  auth.uid() = user_id
  OR
  -- Public documents  
  is_public = true
  OR
  -- Documents shared through rooms
  id IN (
    SELECT rd.document_id FROM room_documents rd
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

-- Step 5: Update document_shares policies
DROP POLICY IF EXISTS "Users can view shares involving them" ON document_shares;

CREATE POLICY "Users can view shares involving them" ON document_shares FOR SELECT USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

CREATE POLICY "Users can create permanent shares" ON document_shares FOR INSERT WITH CHECK (
  -- Users can create shares for documents they own
  original_document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
  OR
  -- Users can create shares for documents they have permission to share
  shared_by = auth.uid()
);

-- Step 6: Create function to handle permanent document sharing
CREATE OR REPLACE FUNCTION create_permanent_document_share(
  p_original_document_id UUID,
  p_shared_by UUID,
  p_shared_with UUID,
  p_permission TEXT DEFAULT 'download'
) RETURNS UUID AS $$
DECLARE
  v_original_doc documents%ROWTYPE;
  v_new_document_id UUID;
  v_new_file_path TEXT;
  v_share_id UUID;
BEGIN
  -- Get original document
  SELECT * INTO v_original_doc 
  FROM documents 
  WHERE id = p_original_document_id AND user_id = p_shared_by;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Document not found or access denied';
  END IF;
  
  -- Generate new file path for the copy
  v_new_file_path := p_shared_with || '/documents/' || 
                     extract(epoch from now())::bigint || '_shared_' || 
                     split_part(v_original_doc.file_path, '/', -1);
  
  -- Create new document record for recipient
  INSERT INTO documents (
    user_id,
    folder_id,
    title,
    description,
    file_path,
    file_size,
    file_type,
    mime_type,
    is_public,
    is_shared_copy,
    original_document_id,
    shared_from_user_id,
    shared_at
  ) VALUES (
    p_shared_with,
    NULL, -- Goes to root folder
    v_original_doc.title || ' (shared by ' || p_shared_by || ')',
    v_original_doc.description,
    v_new_file_path,
    v_original_doc.file_size,
    v_original_doc.file_type,
    v_original_doc.mime_type,
    false, -- Shared documents are private by default
    true,
    p_original_document_id,
    p_shared_by,
    NOW()
  ) RETURNING id INTO v_new_document_id;
  
  -- Create share record
  INSERT INTO document_shares (
    original_document_id,
    shared_document_id,
    shared_by,
    shared_with,
    permission,
    is_permanent,
    shared_at,
    original_owner_id
  ) VALUES (
    p_original_document_id,
    v_new_document_id,
    p_shared_by,
    p_shared_with,
    p_permission,
    true,
    NOW(),
    p_shared_by
  ) RETURNING id INTO v_share_id;
  
  RETURN v_new_document_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Create function to get sharing statistics
CREATE OR REPLACE FUNCTION get_user_sharing_stats(p_user_id UUID)
RETURNS TABLE (
  documents_shared_by_me BIGINT,
  documents_shared_with_me BIGINT,
  total_documents BIGINT,
  owned_documents BIGINT,
  shared_copies BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM document_shares WHERE shared_by = p_user_id AND is_permanent = true),
    (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id AND is_shared_copy = true),
    (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id),
    (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id AND is_shared_copy = false),
    (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id AND is_shared_copy = true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Grant necessary permissions
GRANT EXECUTE ON FUNCTION create_permanent_document_share TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_sharing_stats TO authenticated;
