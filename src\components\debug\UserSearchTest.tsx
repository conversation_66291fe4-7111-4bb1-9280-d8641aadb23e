import { useState } from "react";
import { userService } from "../../lib/userService";
import { useAuth } from "../../contexts/AuthContext";
import type { UserSearchResult } from "../../lib/userService";

export function UserSearchTest() {
  const { user } = useAuth();
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!user || !query.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const searchResults = await userService.searchUsers(query, user.id);
      setResults(searchResults);

      if (searchResults.length === 0) {
        setError(
          "No users found. This could indicate a database policy issue."
        );
      }
    } catch (err: any) {
      setError(`Search failed: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDirectQuery = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // Test direct profile access
      const profile = await userService.getUserProfile(user.id);

      if (!profile) {
        setError(
          "Cannot access own profile - profiles table might be empty or RLS is blocking access"
        );
      } else {
        setError(null);
      }
    } catch (err: any) {
      console.error("Profile access error:", err);
      setError(`Profile access failed: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return <div>Please log in to test user search</div>;
  }

  return (
    <div className="p-6 bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg transition-theme duration-theme">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
        User Search Debug Tool
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme">
            Search Query
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter name or email to search..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <button
              onClick={handleSearch}
              disabled={isLoading || !query.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Searching..." : "Search"}
            </button>
          </div>
        </div>

        <div>
          <button
            onClick={testDirectQuery}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Test Profile Access
          </button>
        </div>

        {error && (
          <div className="p-3 bg-red-100 border border-red-300 rounded-md">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {results.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-2">
              Search Results ({results.length})
            </h4>
            <div className="space-y-2">
              {results.map((result) => (
                <div
                  key={result.id}
                  className="p-3 bg-gray-50 border border-gray-200 rounded-md"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-medium text-sm">
                        {userService.getUserInitials(result)}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {userService.getUserDisplayName(result)}
                      </p>
                      <p className="text-sm text-gray-500">{result.email}</p>
                      <p className="text-xs text-gray-400 capitalize">
                        {result.role}{" "}
                        {result.institution && `• ${result.institution}`}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Debug Info</h4>
          <div className="text-xs text-gray-600 space-y-1">
            <p>Current User ID: {user.id}</p>
            <p>Current User Email: {user.email}</p>
            <p>Current User Name: {user.profile?.full_name || "Not set"}</p>
            <p>Current User Role: {user.profile?.role || "Not set"}</p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            Troubleshooting
          </h4>
          <div className="text-xs text-blue-700 space-y-1">
            <p>
              1. Make sure you've run the database/fix-user-search-policies.sql
              script
            </p>
            <p>
              2. Check that the profiles table has data (users need to be
              registered)
            </p>
            <p>
              3. Verify RLS policies allow authenticated users to search
              profiles
            </p>
            <p>4. Check browser console for detailed error messages</p>
          </div>
        </div>
      </div>
    </div>
  );
}
