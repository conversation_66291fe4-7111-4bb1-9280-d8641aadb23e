-- Fix RLS policies for permanent document sharing
-- Run this in your Supabase SQL editor
-- This script safely handles existing policies to avoid "already exists" errors

-- Step 1: Add new columns to documents table if they don't exist
-- Note: original_document_id is nullable and doesn't prevent deletion of original
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS is_shared_copy BO<PERSON>EAN DEFAULT false,
ADD COLUMN IF NOT EXISTS original_document_id UUID,
ADD COLUMN IF NOT EXISTS shared_from_user_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS shared_at TIMESTAMP WITH TIME ZONE;

-- Remove foreign key constraint if it exists to allow independent document deletion
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints
               WHERE constraint_name = 'documents_original_document_id_fkey') THEN
        ALTER TABLE documents DROP CONSTRAINT documents_original_document_id_fkey;
    END IF;
END $$;

-- Step 2: Add new columns to document_shares table if they don't exist
ALTER TABLE document_shares
ADD COLUMN IF NOT EXISTS is_permanent BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS original_owner_id UUID REFERENCES auth.users(id);

-- Step 3: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_documents_shared_copy ON documents(is_shared_copy);
CREATE INDEX IF NOT EXISTS idx_documents_original_document_id ON documents(original_document_id);
CREATE INDEX IF NOT EXISTS idx_document_shares_is_permanent ON document_shares(is_permanent);

-- Step 4: Update storage policies - Drop all existing policies first
DROP POLICY IF EXISTS "Users can upload to own folder" ON storage.objects;
DROP POLICY IF EXISTS "Allow file copying for sharing" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view own files and shared files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own files" ON storage.objects;

-- Create storage INSERT policy (allows sharing uploads)
CREATE POLICY "Users can upload files" ON storage.objects
FOR INSERT WITH CHECK (
  -- Users can upload to their own folder
  (storage.foldername(name))[1] = auth.uid()::text
  OR
  -- Allow uploads to any folder in documents bucket for sharing
  -- This enables cross-user file copying during document sharing
  bucket_id = 'documents'
);

-- Create storage SELECT policy
CREATE POLICY "Users can view files" ON storage.objects
FOR SELECT USING (
  -- Own files (in their folder)
  (storage.foldername(name))[1] = auth.uid()::text
  OR
  -- Files from documents they own (including shared copies)
  name IN (
    SELECT file_path FROM documents WHERE user_id = auth.uid()
  )
  OR
  -- Files from documents shared with them (for backward compatibility)
  name IN (
    SELECT d.file_path
    FROM documents d
    JOIN document_shares ds ON d.id = ds.original_document_id
    WHERE ds.shared_with = auth.uid()
  )
);

-- Create storage UPDATE policy
CREATE POLICY "Users can update own files" ON storage.objects
FOR UPDATE USING (
  (storage.foldername(name))[1] = auth.uid()::text
);

-- Create storage DELETE policy
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (
  (storage.foldername(name))[1] = auth.uid()::text
);

-- Step 5: Update documents table policies - Drop all existing policies first
DROP POLICY IF EXISTS "Users can create documents and share documents" ON documents;
DROP POLICY IF EXISTS "Allow document creation for sharing" ON documents;
DROP POLICY IF EXISTS "Users can create documents" ON documents;
DROP POLICY IF EXISTS "Users can create documents and shared copies" ON documents;
DROP POLICY IF EXISTS "Users can view shared documents" ON documents;
DROP POLICY IF EXISTS "Users can view documents" ON documents;
DROP POLICY IF EXISTS "Users can update own documents" ON documents;
DROP POLICY IF EXISTS "Users can delete own documents" ON documents;

-- Create documents INSERT policy (allows sharing)
CREATE POLICY "Users can create documents and shared copies" ON documents
FOR INSERT WITH CHECK (
  -- Users can create documents for themselves
  auth.uid() = user_id
  OR
  -- Allow creating shared copies for other users (when sharing)
  -- This allows User A to create a document owned by User B when sharing
  (is_shared_copy = true AND auth.uid() IS NOT NULL)
  OR
  -- Allow creating documents for other users during sharing process
  -- This is needed for permanent document sharing where we create a copy for the recipient
  (auth.uid() IS NOT NULL AND user_id IS NOT NULL)
);

-- Create documents SELECT policy
CREATE POLICY "Users can view documents" ON documents FOR SELECT USING (
  -- Own documents (including shared copies)
  auth.uid() = user_id
  OR
  -- Public documents
  is_public = true
  OR
  -- Documents shared through rooms
  id IN (
    SELECT rd.document_id FROM room_documents rd
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

-- Create documents UPDATE policy
CREATE POLICY "Users can update own documents" ON documents
FOR UPDATE USING (auth.uid() = user_id);

-- Create documents DELETE policy
CREATE POLICY "Users can delete own documents" ON documents
FOR DELETE USING (auth.uid() = user_id);

-- Step 6: Update document_shares policies - Drop existing policies first
DROP POLICY IF EXISTS "Users can view shares involving them" ON document_shares;
DROP POLICY IF EXISTS "Users can create shares for their documents" ON document_shares;
DROP POLICY IF EXISTS "Users can delete their shares" ON document_shares;
DROP POLICY IF EXISTS "Users can create permanent shares" ON document_shares;

-- Create document_shares SELECT policy
CREATE POLICY "Users can view shares involving them" ON document_shares FOR SELECT USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

-- Create document_shares INSERT policy
CREATE POLICY "Users can create shares for their documents" ON document_shares FOR INSERT WITH CHECK (
  original_document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
  OR shared_by = auth.uid()
);

-- Create document_shares DELETE policy
CREATE POLICY "Users can delete their shares" ON document_shares FOR DELETE USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

-- Verification queries (optional - uncomment to test)
-- SELECT tablename, policyname, cmd, qual FROM pg_policies WHERE tablename IN ('documents', 'objects') ORDER BY tablename, policyname;
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'documents' AND column_name LIKE '%shared%';
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'document_shares' AND column_name LIKE '%permanent%';
