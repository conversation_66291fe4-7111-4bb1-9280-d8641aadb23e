-- SmartBagPack Admin Authentication Setup (Fixed)
-- Run this SQL in your Supabase SQL Editor to fix the RLS policy conflicts

-- First, drop the problematic policies that are causing infinite recursion
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update user roles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all documents" ON documents;
DROP POLICY IF EXISTS "Ad<PERSON> can delete documents" ON documents;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can manage rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can delete rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can view all room members" ON room_members;
DROP POLICY IF EXISTS "Ad<PERSON> can manage room members" ON room_members;
DROP POLICY IF EXISTS "Ad<PERSON> can view all folders" ON folders;
DROP POLICY IF EXISTS "Ad<PERSON> can view all document shares" ON document_shares;
DROP POLICY IF EXISTS "Ad<PERSON> can view all room documents" ON room_documents;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all notifications" ON notifications;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all invitations" ON room_invitations;
DROP POLICY IF EXISTS "Admins can view all invitation links" ON room_invitation_links;
DROP POLICY IF EXISTS "Admins can view all documents in storage" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete documents in storage" ON storage.objects;

-- Create admin_actions table for audit logging (if not exists)
CREATE TABLE IF NOT EXISTS admin_actions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  target_type TEXT CHECK (target_type IN ('user', 'document', 'room', 'system')) NOT NULL,
  target_id UUID,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for admin_actions
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_id ON admin_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_created_at ON admin_actions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_actions_action_type ON admin_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_actions_target ON admin_actions(target_type, target_id);

-- Create activity_logs table for user activity tracking (if not exists)
CREATE TABLE IF NOT EXISTS activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  target_type TEXT,
  target_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for activity_logs
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_action_type ON activity_logs(action_type);

-- Enable Row Level Security
ALTER TABLE admin_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for admin_actions (safe policies that don't cause recursion)
CREATE POLICY "Admins can insert admin actions" ON admin_actions FOR INSERT WITH CHECK (
  auth.uid() = admin_id AND 
  auth.jwt() ->> 'role' = 'admin'
);

CREATE POLICY "Admins can view admin actions" ON admin_actions FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- RLS Policies for activity_logs
CREATE POLICY "System can insert activity logs" ON activity_logs FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all activity logs" ON activity_logs FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

CREATE POLICY "Users can view own activity logs" ON activity_logs FOR SELECT USING (
  auth.uid() = user_id
);

-- Safe admin policies that avoid recursion by using auth.jwt() instead of profiles table lookup

-- Admin can view all profiles (using JWT role check)
CREATE POLICY "Admins can view all profiles via JWT" ON profiles FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can update user roles (but not their own, using JWT role check)
CREATE POLICY "Admins can update user roles via JWT" ON profiles FOR UPDATE USING (
  auth.jwt() ->> 'role' = 'admin' AND id != auth.uid()
) WITH CHECK (
  auth.jwt() ->> 'role' = 'admin' AND id != auth.uid()
);

-- Admin can view all documents (using JWT role check)
CREATE POLICY "Admins can view all documents via JWT" ON documents FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can delete any document (using JWT role check)
CREATE POLICY "Admins can delete documents via JWT" ON documents FOR DELETE USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all rooms (using JWT role check)
CREATE POLICY "Admins can view all rooms via JWT" ON rooms FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can update/delete any room (using JWT role check)
CREATE POLICY "Admins can manage rooms via JWT" ON rooms FOR UPDATE USING (
  auth.jwt() ->> 'role' = 'admin'
);

CREATE POLICY "Admins can delete rooms via JWT" ON rooms FOR DELETE USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all room members (using JWT role check)
CREATE POLICY "Admins can view all room members via JWT" ON room_members FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can manage room memberships (using JWT role check)
CREATE POLICY "Admins can manage room members via JWT" ON room_members FOR ALL USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all folders (using JWT role check)
CREATE POLICY "Admins can view all folders via JWT" ON folders FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all document shares (using JWT role check)
CREATE POLICY "Admins can view all document shares via JWT" ON document_shares FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all room documents (using JWT role check)
CREATE POLICY "Admins can view all room documents via JWT" ON room_documents FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all notifications (using JWT role check)
CREATE POLICY "Admins can view all notifications via JWT" ON notifications FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Admin can view all invitations (using JWT role check)
CREATE POLICY "Admins can view all invitations via JWT" ON room_invitations FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

CREATE POLICY "Admins can view all invitation links via JWT" ON room_invitation_links FOR SELECT USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- Storage policies for admin access (using JWT role check)
CREATE POLICY "Admins can view all documents in storage via JWT" ON storage.objects
FOR SELECT USING (
  bucket_id = 'documents' 
  AND auth.jwt() ->> 'role' = 'admin'
);

CREATE POLICY "Admins can delete documents in storage via JWT" ON storage.objects
FOR DELETE USING (
  bucket_id = 'documents' 
  AND auth.jwt() ->> 'role' = 'admin'
);

-- Function to log admin actions (updated to use JWT)
CREATE OR REPLACE FUNCTION log_admin_action(
  p_action_type TEXT,
  p_target_type TEXT,
  p_target_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  action_id UUID;
BEGIN
  -- Check if user is admin using JWT
  IF auth.jwt() ->> 'role' != 'admin' THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;
  
  INSERT INTO admin_actions (admin_id, action_type, target_type, target_id, details)
  VALUES (auth.uid(), p_action_type, p_target_type, p_target_id, p_details)
  RETURNING id INTO action_id;
  
  RETURN action_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id UUID,
  p_action_type TEXT,
  p_target_type TEXT DEFAULT NULL,
  p_target_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO activity_logs (user_id, action_type, target_type, target_id, details, ip_address, user_agent)
  VALUES (p_user_id, p_action_type, p_target_type, p_target_id, p_details, p_ip_address, p_user_agent)
  RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure admin profile exists and update JWT claims
UPDATE auth.users 
SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'),
  '{role}',
  '"admin"'
)
WHERE email = '<EMAIL>';

-- Update the profile to ensure role is set
UPDATE profiles 
SET role = 'admin', updated_at = NOW()
WHERE email = '<EMAIL>';

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
