import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { profileService } from "../lib/profileService";
import { fileService } from "../lib/fileService";
import { formatFileSize, formatDateTime } from "../lib/utils";
import { usePageTitle } from "../hooks/usePageTitle";
import { FeedbackModal } from "../components/feedback/FeedbackModal";

const profileSchema = z.object({
  full_name: z
    .string()
    .min(1, "Full name is required")
    .max(100, "Name too long"),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  role: string;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}

interface UserStats {
  totalDocuments: number;
  storageUsed: number;
  documentsShared: number;
  documentsReceived: number;
}

export function Profile() {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  // Set page title
  usePageTitle("Profile");

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<UserStats>({
    totalDocuments: 0,
    storageUsed: 0,
    documentsShared: 0,
    documentsReceived: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<ProfileFormData>();

  useEffect(() => {
    if (user) {
      loadProfile();
      loadStats();
    }
  }, [user]);

  const loadProfile = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userProfile = await profileService.getUserProfile(user.id);
      setProfile(userProfile);

      // Set form values
      setValue("full_name", userProfile.full_name || "");
    } catch (error: any) {
      console.error("Failed to load profile:", error);
      toast.error("Failed to load profile");
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async () => {
    if (!user) return;

    try {
      const userStats = await fileService.getUserStatistics(user.id);
      setStats({
        totalDocuments: userStats.totalDocuments,
        storageUsed: userStats.storageUsed,
        documentsShared: 0, // TODO: Implement shared documents count
        documentsReceived: 0, // TODO: Implement received documents count
      });
    } catch (error: any) {
      console.error("Failed to load stats:", error);
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;

    try {
      setIsUpdating(true);
      await profileService.updateUserProfile(user.id, {
        full_name: data.full_name,
      });

      toast.success("Profile updated successfully!");
      await loadProfile(); // Reload profile to get updated data
    } catch (error: any) {
      toast.error(error.message || "Failed to update profile");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!user) return;

    try {
      setIsDeleting(true);
      await profileService.deleteUserAccount(user.id);
      toast.success("Account deleted successfully");

      // Sign out the user and redirect to login
      await signOut();
      navigate("/login");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete account");
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
              Profile Settings
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
              Manage your account information and preferences
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Form */}
            <div className="lg:col-span-2">
              <div className="card p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-theme duration-theme">
                  Personal Information
                </h2>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Email (read-only) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={user?.email || ""}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md bg-gray-50 dark:bg-dark-700 text-gray-500 dark:text-gray-400 cursor-not-allowed transition-theme duration-theme"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                      Email cannot be changed
                    </p>
                  </div>

                  {/* Full Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      {...register("full_name")}
                      disabled={isUpdating}
                      className="input-field"
                      placeholder="Enter your full name"
                    />
                    {errors.full_name && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                        {errors.full_name.message}
                      </p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="btn-primary flex items-center space-x-2"
                    >
                      {isUpdating && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      )}
                      <span>
                        {isUpdating ? "Updating..." : "Update Profile"}
                      </span>
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Profile Stats */}
            <div className="space-y-6">
              {/* Account Info */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
                  Account Information
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Member since
                    </span>
                    <span className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {profile?.created_at
                        ? formatDateTime(profile.created_at).split(" ")[0]
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Last updated
                    </span>
                    <span className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {profile?.updated_at
                        ? formatDateTime(profile.updated_at).split(" ")[0]
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      User ID
                    </span>
                    <span className="font-mono text-xs bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded transition-theme duration-theme">
                      {user?.id.slice(0, 8)}...
                    </span>
                  </div>
                </div>
              </div>

              {/* Usage Statistics */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
                  Usage Statistics
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-5 h-5 text-blue-600 dark:text-blue-400 transition-theme duration-theme"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Documents
                      </span>
                    </div>
                    <span className="font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.totalDocuments}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-5 h-5 text-green-600 dark:text-green-400 transition-theme duration-theme"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
                        />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Storage Used
                      </span>
                    </div>
                    <span className="font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {formatFileSize(stats.storageUsed)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-5 h-5 text-purple-600 dark:text-purple-400 transition-theme duration-theme"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                        />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Shared
                      </span>
                    </div>
                    <span className="font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.documentsShared}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-5 h-5 text-orange-600 dark:text-orange-400 transition-theme duration-theme"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Received
                      </span>
                    </div>
                    <span className="font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.documentsReceived}
                    </span>
                  </div>
                </div>
              </div>

              {/* Feedback Section */}
              <div className="card p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">💬</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      Send Feedback
                    </h3>
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Help us improve BrimBag by sharing your thoughts,
                      reporting bugs, or suggesting new features.
                    </p>
                    <div className="mt-4">
                      <button
                        onClick={() => setShowFeedbackModal(true)}
                        className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-lg transition-all duration-200 min-h-[44px] flex items-center space-x-2"
                      >
                        <span>💬</span>
                        <span>Share Feedback</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Danger Zone */}
              <div className="card p-6 border-red-200 dark:border-red-800">
                <h3 className="text-lg font-semibold text-red-600 dark:text-red-400 mb-4 transition-theme duration-theme">
                  Danger Zone
                </h3>
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 transition-theme duration-theme">
                  <div className="flex items-start space-x-3">
                    <svg
                      className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0 transition-theme duration-theme"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-red-800 dark:text-red-200 transition-theme duration-theme">
                        Complete Account Deletion
                      </h4>
                      <p className="mt-1 text-sm text-red-700 dark:text-red-300 transition-theme duration-theme">
                        Permanently delete your account and all associated data
                        including your authentication record. This allows you to
                        re-register with the same email if desired. This action
                        cannot be undone.
                      </p>
                      <div className="mt-3">
                        <button
                          onClick={() => setShowDeleteConfirm(true)}
                          className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 rounded-md transition-all duration-200 min-h-[44px]"
                        >
                          Delete Account Completely
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-xl max-w-md w-full transition-theme duration-theme">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <svg
                  className="w-6 h-6 text-red-600 dark:text-red-400 transition-theme duration-theme"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  Complete Account Deletion
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 transition-theme duration-theme">
                  Are you sure you want to completely delete your account? This
                  will permanently remove:
                </p>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1 ml-4 transition-theme duration-theme">
                  <li>• All your documents and files</li>
                  <li>• All your folders and organization</li>
                  <li>• All shared documents and room memberships</li>
                  <li>• All rooms you created</li>
                  <li>• Your profile and account data</li>
                  <li>• Your authentication record (allows re-registration)</li>
                </ul>
                <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg transition-theme duration-theme">
                  <p className="text-sm text-green-700 dark:text-green-300 transition-theme duration-theme">
                    ✓ After deletion, you will be able to register again with
                    the same email address if desired.
                  </p>
                </div>
                <p className="text-sm text-red-600 dark:text-red-400 mt-4 font-medium transition-theme duration-theme">
                  This action cannot be undone.
                </p>
              </div>

              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteAccount}
                  disabled={isDeleting}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 min-h-[44px] transition-all duration-200"
                >
                  {isDeleting && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  <span>{isDeleting ? "Deleting..." : "Delete Account"}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Feedback Modal */}
      {showFeedbackModal && user && (
        <FeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          userId={user.id}
          onFeedbackSubmitted={() => {
            // Optional: Could refresh user stats or show additional confirmation
            console.log("Feedback submitted successfully");
          }}
        />
      )}
    </DashboardLayout>
  );
}
