-- SmartBagPack User Management Functions
-- Run this SQL in your Supabase SQL Editor to add user management capabilities

-- Add is_active field to profiles table if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Create index for is_active field
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);

-- Function to get user statistics for admin dashboard
CREATE OR REPLACE FUNCTION get_user_statistics(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_stats JSON;
BEGIN
  SELECT json_build_object(
    'document_count', COALESCE(doc_count, 0),
    'storage_used', COALESCE(storage_used, 0),
    'rooms_joined', COALESCE(rooms_joined, 0),
    'rooms_created', COALESCE(rooms_created, 0),
    'documents_shared_with_user', COALESCE(shared_docs, 0),
    'recent_uploads_7d', COALESCE(recent_uploads, 0),
    'last_activity', last_activity_date,
    'registration_date', registration_date
  ) INTO user_stats
  FROM (
    SELECT 
      p.created_at as registration_date,
      (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id) as doc_count,
      (SELECT COALESCE(SUM(file_size), 0) FROM documents WHERE user_id = p_user_id) as storage_used,
      (SELECT COUNT(*) FROM room_members WHERE user_id = p_user_id) as rooms_joined,
      (SELECT COUNT(*) FROM rooms WHERE created_by = p_user_id) as rooms_created,
      (SELECT COUNT(*) FROM document_shares WHERE shared_with_user_id = p_user_id) as shared_docs,
      (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id AND created_at > NOW() - INTERVAL '7 days') as recent_uploads,
      (SELECT MAX(created_at) FROM activity_logs WHERE user_id = p_user_id) as last_activity_date
    FROM profiles p
    WHERE p.id = p_user_id
  ) stats;
  
  RETURN user_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing function first to ensure clean recreation
DROP FUNCTION IF EXISTS admin_delete_user(UUID);

-- Function to safely delete user and all associated data
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  admin_actions INTEGER := 0;
BEGIN
  -- Check if current user is admin OR deleting their own account
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
     AND auth.uid() != p_user_id THEN
    RAISE EXCEPTION 'Access denied: Admin role required or can only delete own account';
  END IF;

  -- Allow self-deletion for regular users, but prevent admin self-deletion
  IF auth.uid() = p_user_id AND EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Admin users cannot delete their own account';
  END IF;

  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;

  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Count records before deletion for logging
  SELECT COUNT(*) INTO documents FROM documents WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO folders FROM folders WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO room_memberships FROM room_members WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO rooms_created FROM rooms WHERE created_by = p_user_id;
  SELECT COUNT(*) INTO document_shares FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  SELECT COUNT(*) INTO activity_logs FROM activity_logs WHERE user_id = p_user_id;
  SELECT COUNT(*) INTO admin_actions FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Count items before deletion for reporting
  WITH deletion_counts AS (
    SELECT 
      (SELECT COUNT(*) FROM documents WHERE user_id = p_user_id) as documents,
      (SELECT COUNT(*) FROM folders WHERE user_id = p_user_id) as folders,
      (SELECT COUNT(*) FROM room_members WHERE user_id = p_user_id) as room_memberships,
      (SELECT COUNT(*) FROM rooms WHERE created_by = p_user_id) as rooms_created,
      (SELECT COUNT(*) FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id) as document_shares,
      (SELECT COUNT(*) FROM activity_logs WHERE user_id = p_user_id) as activity_logs,
      (SELECT COUNT(*) FROM admin_actions WHERE admin_id = p_user_id) as admin_actions
  )
  SELECT json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'admin_actions_removed', admin_actions
  ) INTO deleted_counts
  FROM deletion_counts;
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete admin actions
  DELETE FROM admin_actions WHERE admin_id = p_user_id;
  
  -- Delete document shares (using correct column names)
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user (FIXED: use shared_by not user_id)
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications (FIXED: notifications table only has user_id, no sender_id)
  DELETE FROM notifications WHERE user_id = p_user_id;

  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;

  -- Delete room invitations (FIXED: use invited_user not invited_user_id)
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;

  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;

  -- Note: We don't delete from auth.users here as it's handled by the client-side admin API
  -- This ensures proper cleanup of the authentication record

  -- Log the admin action (only if not self-deletion)
  IF auth.uid() != p_user_id THEN
    PERFORM log_admin_action(
      'user_delete',
      'user',
      p_user_id,
      deleted_counts
    );
  END IF;

  RETURN deleted_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing function first to allow return type change
DROP FUNCTION IF EXISTS admin_update_user_status(UUID, BOOLEAN, TEXT);

-- Function to update user status (activate/deactivate)
CREATE OR REPLACE FUNCTION admin_update_user_status(
  p_user_id UUID,
  p_active BOOLEAN,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_admin_id UUID;
  v_user_record RECORD;
  v_result JSON;
BEGIN
  -- Get the current admin user ID
  v_admin_id := auth.uid();

  -- Verify admin access
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = v_admin_id
    AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;

  -- Get user record before update
  SELECT * INTO v_user_record FROM profiles WHERE id = p_user_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Update user status (bypasses RLS)
  UPDATE profiles
  SET
    active = p_active,
    deactivation_reason = CASE WHEN p_active THEN NULL ELSE p_reason END,
    deactivated_at = CASE WHEN p_active THEN NULL ELSE NOW() END,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Log the action
  INSERT INTO activity_logs (
    user_id,
    action_type,
    target_type,
    target_id,
    details
  ) VALUES (
    v_admin_id,
    CASE WHEN p_active THEN 'user_activate' ELSE 'user_deactivate' END,
    'user',
    p_user_id,
    jsonb_build_object(
      'target_user_email', v_user_record.email,
      'target_user_name', v_user_record.full_name,
      'previous_status', v_user_record.active,
      'new_status', p_active,
      'reason', COALESCE(p_reason, 'No reason provided'),
      'timestamp', NOW()
    )
  );

  -- Return success result
  v_result := json_build_object(
    'success', true,
    'message', CASE WHEN p_active THEN 'User activated successfully' ELSE 'User deactivated successfully' END,
    'user_id', p_user_id,
    'new_status', p_active
  );

  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to bulk update user roles
CREATE OR REPLACE FUNCTION admin_bulk_update_roles(
  p_user_ids UUID[],
  p_new_role TEXT
)
RETURNS JSON AS $$
DECLARE
  success_count INTEGER := 0;
  failed_count INTEGER := 0;
  user_id UUID;
  user_info RECORD;
  errors TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;
  
  -- Validate role
  IF p_new_role NOT IN ('student', 'lecturer', 'admin') THEN
    RAISE EXCEPTION 'Invalid role: %', p_new_role;
  END IF;
  
  -- Process each user
  FOREACH user_id IN ARRAY p_user_ids
  LOOP
    BEGIN
      -- Skip self
      IF auth.uid() = user_id THEN
        failed_count := failed_count + 1;
        errors := array_append(errors, 'Cannot change your own role');
        CONTINUE;
      END IF;
      
      -- Get user info
      SELECT email, full_name, role INTO user_info
      FROM profiles WHERE id = user_id;
      
      IF user_info IS NULL THEN
        failed_count := failed_count + 1;
        errors := array_append(errors, 'User not found: ' || user_id::TEXT);
        CONTINUE;
      END IF;
      
      -- Update role
      UPDATE profiles 
      SET role = p_new_role, updated_at = NOW()
      WHERE id = user_id;
      
      -- Log the action
      PERFORM log_admin_action(
        'user_role_change',
        'user',
        user_id,
        json_build_object(
          'user_email', user_info.email,
          'user_name', user_info.full_name,
          'old_role', user_info.role,
          'new_role', p_new_role
        )
      );
      
      success_count := success_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      failed_count := failed_count + 1;
      errors := array_append(errors, 'Error updating user ' || user_id::TEXT || ': ' || SQLERRM);
    END;
  END LOOP;
  
  -- Log bulk action
  PERFORM log_admin_action(
    'bulk_action',
    'user',
    NULL,
    json_build_object(
      'action', 'bulk_role_update',
      'new_role', p_new_role,
      'user_count', array_length(p_user_ids, 1),
      'success_count', success_count,
      'failed_count', failed_count,
      'errors', errors
    )
  );
  
  RETURN json_build_object(
    'success', success_count,
    'failed', failed_count,
    'errors', errors
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get comprehensive activity statistics
CREATE OR REPLACE FUNCTION get_activity_statistics()
RETURNS JSON AS $$
DECLARE
  stats JSON;
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;
  
  WITH activity_stats AS (
    SELECT 
      COUNT(*) as total_activities,
      COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as activities_today,
      COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as activities_this_week,
      COUNT(DISTINCT user_id) FILTER (WHERE created_at >= CURRENT_DATE) as active_users_today
    FROM activity_logs
  ),
  top_actions AS (
    SELECT 
      action_type,
      COUNT(*) as count
    FROM activity_logs 
    WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
    GROUP BY action_type
    ORDER BY count DESC
    LIMIT 5
  )
  SELECT json_build_object(
    'total_activities', s.total_activities,
    'activities_today', s.activities_today,
    'activities_this_week', s.activities_this_week,
    'active_users_today', s.active_users_today,
    'top_actions', COALESCE(
      (SELECT json_agg(json_build_object('action_type', action_type, 'count', count)) FROM top_actions),
      '[]'::json
    )
  ) INTO stats
  FROM activity_stats s;
  
  RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_statistics(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_update_user_status(UUID, BOOLEAN, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_bulk_update_roles(UUID[], TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_activity_statistics() TO authenticated;
