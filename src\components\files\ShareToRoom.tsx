import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { roomService } from "../../lib/roomService";
import type { RoomWithDetails } from "../../lib/roomService";
import { useAuth } from "../../contexts/AuthContext";

const shareSchema = z.object({
  roomId: z.string().min(1, "Please select a room"),
  permission: z.enum(["view", "download"]),
  roomCode: z.string().optional(),
});

type ShareFormData = z.infer<typeof shareSchema>;

interface ShareToRoomProps {
  document: DocumentFile;
  onClose: () => void;
  onShareComplete?: () => void;
}

export function ShareToRoom({
  document,
  onClose,
  onShareComplete,
}: ShareToRoomProps) {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<RoomWithDetails[]>([]);
  const [searchResults, setSearchResults] = useState<RoomWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [shareMode, setShareMode] = useState<"select" | "search" | "code">(
    "select"
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ShareFormData>({
    defaultValues: {
      permission: "download",
    },
  });

  const selectedRoomId = watch("roomId");

  useEffect(() => {
    loadUserRooms();
  }, []);

  const loadUserRooms = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userRooms = await roomService.getUserRooms(user.id);
      setRooms(userRooms);
    } catch (error: any) {
      console.error("Load user rooms error:", error);
      if (
        error.message?.includes("parse logic tree") ||
        error.message?.includes("policy")
      ) {
        toast.error(
          "Database query issue. Please try again or contact support."
        );
      } else {
        toast.error(error.message || "Failed to load rooms");
      }
      setRooms([]); // Set empty array to prevent crashes
    } finally {
      setIsLoading(false);
    }
  };

  const searchRooms = async (query: string) => {
    if (!user || !query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await roomService.searchRooms(query, user.id);
      setSearchResults(results);
    } catch (error: any) {
      console.error("Search rooms error:", error);
      if (
        error.message?.includes("parse logic tree") ||
        error.message?.includes("policy")
      ) {
        toast.error(
          "Database query issue. Please try again or contact support."
        );
      } else {
        toast.error(error.message || "Failed to search rooms");
      }
      setSearchResults([]); // Set empty array to prevent crashes
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    if (query.length >= 2) {
      searchRooms(query);
    } else {
      setSearchResults([]);
    }
  };

  const joinRoomByCode = async (roomCode: string) => {
    if (!user || !roomCode.trim()) return;

    try {
      await roomService.joinRoom(roomCode.toUpperCase(), user.id);
      toast.success("Joined room successfully!");
      loadUserRooms();
    } catch (error: any) {
      toast.error(error.message || "Failed to join room");
    }
  };

  const onSubmit = async (data: ShareFormData) => {
    if (!user) return;

    try {
      setIsLoading(true);

      let roomId = data.roomId;

      // If sharing by room code, find the room first
      if (shareMode === "code" && data.roomCode) {
        const searchResults = await roomService.searchRooms(
          data.roomCode,
          user.id
        );
        const room = searchResults.find(
          (r) => r.room_code === data.roomCode?.toUpperCase()
        );

        if (!room) {
          toast.error("Room not found");
          return;
        }

        if (!room.is_member) {
          await joinRoomByCode(data.roomCode);
        }

        roomId = room.id;
      }

      await roomService.shareDocumentToRoom(
        document.id,
        roomId,
        user.id,
        data.permission
      );

      toast.success("Document shared to room successfully!");
      onShareComplete?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to share document");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Share to Room</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="px-6 py-4">
          {/* Document Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">📄</span>
              <div>
                <p className="font-medium text-gray-900">{document.title}</p>
                <p className="text-sm text-gray-600">{document.file_type}</p>
              </div>
            </div>
          </div>

          {/* Share Mode Tabs */}
          <div className="mb-6">
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setShareMode("select")}
                className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                  shareMode === "select"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                My Rooms
              </button>
              <button
                onClick={() => setShareMode("search")}
                className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                  shareMode === "search"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Search
              </button>
              <button
                onClick={() => setShareMode("code")}
                className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                  shareMode === "code"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Room Code
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Room Selection */}
            {shareMode === "select" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Room
                </label>
                {isLoading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                ) : rooms.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    <p>You haven't joined any rooms yet.</p>
                    <p className="text-sm">
                      Try searching for rooms or use a room code.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {rooms.map((room) => (
                      <label
                        key={room.id}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          selectedRoomId === room.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200"
                        }`}
                      >
                        <input
                          {...register("roomId")}
                          type="radio"
                          value={room.id}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-gray-900">
                              {room.name}
                            </p>
                            <span className="text-xs text-gray-500">
                              {room.room_code}
                            </span>
                          </div>
                          {room.description && (
                            <p className="text-sm text-gray-600 truncate">
                              {room.description}
                            </p>
                          )}
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            <span>{room.member_count} members</span>
                            <span>{room.document_count} documents</span>
                            <span
                              className={`px-2 py-0.5 rounded ${
                                room.user_role === "admin"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {room.user_role}
                            </span>
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
                {errors.roomId && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.roomId.message}
                  </p>
                )}
              </div>
            )}

            {/* Room Search */}
            {shareMode === "search" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Rooms
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="Search by room name or code..."
                  className="input-field mb-3"
                />
                {isSearching ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                ) : searchResults.length === 0 && searchQuery.length >= 2 ? (
                  <div className="text-center py-4 text-gray-500">
                    No rooms found matching "{searchQuery}"
                  </div>
                ) : (
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {searchResults.map((room) => (
                      <label
                        key={room.id}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          selectedRoomId === room.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200"
                        }`}
                      >
                        <input
                          {...register("roomId")}
                          type="radio"
                          value={room.id}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-gray-900">
                              {room.name}
                            </p>
                            <span className="text-xs text-gray-500">
                              {room.room_code}
                            </span>
                          </div>
                          {room.description && (
                            <p className="text-sm text-gray-600 truncate">
                              {room.description}
                            </p>
                          )}
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            <span>{room.member_count} members</span>
                            <span>{room.document_count} documents</span>
                            {!room.is_member && (
                              <span className="px-2 py-0.5 rounded bg-yellow-100 text-yellow-800">
                                Will join on share
                              </span>
                            )}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Room Code */}
            {shareMode === "code" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Room Code
                </label>
                <input
                  {...register("roomCode")}
                  type="text"
                  placeholder="Enter 6-character room code"
                  className="input-field"
                  maxLength={6}
                  style={{ textTransform: "uppercase" }}
                />
                <p className="mt-1 text-sm text-gray-500">
                  You'll automatically join the room when sharing
                </p>
              </div>
            )}

            {/* Permission Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Permission Level
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    {...register("permission")}
                    type="radio"
                    value="download"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">Download</p>
                    <p className="text-sm text-gray-600">
                      Members can view and download the document
                    </p>
                  </div>
                </label>
                <label className="flex items-center">
                  <input
                    {...register("permission")}
                    type="radio"
                    value="view"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">View Only</p>
                    <p className="text-sm text-gray-600">
                      Members can only view the document
                    </p>
                  </div>
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={
                  isLoading || (shareMode !== "code" && !selectedRoomId)
                }
                className="btn-primary"
              >
                {isLoading ? "Sharing..." : "Share Document"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
