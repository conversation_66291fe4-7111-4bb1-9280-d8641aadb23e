import { supabase } from "./supabase";
import type { Database } from "./supabase";

// Type definitions
export type Notification = Database["public"]["Tables"]["notifications"]["Row"];
export type NotificationType = Notification["type"];

export interface NotificationWithProfile extends Notification {
  sender_profile?: {
    full_name: string | null;
    email: string;
    avatar_url: string | null;
  };
}

export const notificationService = {
  // Create a new notification
  async createNotification(data: {
    userId: string;
    type: NotificationType;
    title: string;
    message: string;
    data?: Record<string, any>;
  }): Promise<Notification> {
    try {
      const { data: notification, error } = await supabase
        .from("notifications")
        .insert({
          user_id: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data || {},
          is_read: false,
        })
        .select()
        .single();

      if (error) throw error;

      return notification;
    } catch (error: any) {
      console.error("Create notification error:", error);
      throw new Error(error.message || "Failed to create notification");
    }
  },

  // Get user's notifications
  async getUserNotifications(
    userId: string,
    limit: number = 50
  ): Promise<NotificationWithProfile[]> {
    try {
      const { data: notifications, error } = await supabase
        .from("notifications")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .limit(limit);

      if (error) throw error;

      return notifications as NotificationWithProfile[];
    } catch (error: any) {
      console.error("Get user notifications error:", error);
      throw new Error(error.message || "Failed to get notifications");
    }
  },

  // Get unread notification count
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId)
        .eq("is_read", false);

      if (error) throw error;

      return count || 0;
    } catch (error: any) {
      console.error("Get unread count error:", error);
      return 0;
    }
  },

  // Mark notification as read
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("notifications")
        .update({
          is_read: true,
          updated_at: new Date().toISOString(),
        })
        .eq("id", notificationId)
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Mark as read error:", error);
      throw new Error(error.message || "Failed to mark notification as read");
    }
  },

  // Mark all notifications as read
  async markAllAsRead(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("notifications")
        .update({
          is_read: true,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", userId)
        .eq("is_read", false);

      if (error) throw error;
    } catch (error: any) {
      console.error("Mark all as read error:", error);
      throw new Error(
        error.message || "Failed to mark all notifications as read"
      );
    }
  },

  // Delete notification
  async deleteNotification(
    notificationId: string,
    userId: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("id", notificationId)
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Delete notification error:", error);
      throw new Error(error.message || "Failed to delete notification");
    }
  },

  // Delete all notifications for user
  async deleteAllNotifications(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Delete all notifications error:", error);
      throw new Error(error.message || "Failed to delete all notifications");
    }
  },

  // Notification creation helpers for specific events
  async notifyRoomInvitation(data: {
    invitedUserId: string;
    inviterName: string;
    roomName: string;
    roomId: string;
    invitationId: string;
  }): Promise<void> {
    await this.createNotification({
      userId: data.invitedUserId,
      type: "room_invitation",
      title: "Room Invitation",
      message: `${data.inviterName} invited you to join "${data.roomName}"`,
      data: {
        room_id: data.roomId,
        room_name: data.roomName,
        invitation_id: data.invitationId,
        inviter_name: data.inviterName,
      },
    });
  },

  async notifyRoomJoin(data: {
    roomAdminId: string;
    memberName: string;
    roomName: string;
    roomId: string;
    memberId: string;
  }): Promise<void> {
    await this.createNotification({
      userId: data.roomAdminId,
      type: "room_join",
      title: "New Room Member",
      message: `${data.memberName} joined your room "${data.roomName}"`,
      data: {
        room_id: data.roomId,
        room_name: data.roomName,
        member_id: data.memberId,
        member_name: data.memberName,
      },
    });
  },

  async notifyRoomLeave(data: {
    roomAdminId: string;
    memberName: string;
    roomName: string;
    roomId: string;
    memberId: string;
  }): Promise<void> {
    await this.createNotification({
      userId: data.roomAdminId,
      type: "room_leave",
      title: "Member Left Room",
      message: `${data.memberName} left your room "${data.roomName}"`,
      data: {
        room_id: data.roomId,
        room_name: data.roomName,
        member_id: data.memberId,
        member_name: data.memberName,
      },
    });
  },

  async notifyDocumentSharedToRoom(data: {
    memberIds: string[];
    sharerName: string;
    documentTitle: string;
    roomName: string;
    roomId: string;
    documentId: string;
  }): Promise<void> {
    const notifications = data.memberIds.map((memberId) => ({
      userId: memberId,
      type: "document_shared_to_room" as NotificationType,
      title: "New Document in Room",
      message: `${data.sharerName} shared "${data.documentTitle}" in "${data.roomName}"`,
      data: {
        room_id: data.roomId,
        room_name: data.roomName,
        document_id: data.documentId,
        document_title: data.documentTitle,
        sharer_name: data.sharerName,
      },
    }));

    // Create notifications in batch
    for (const notification of notifications) {
      await this.createNotification(notification);
    }
  },

  async notifyDocumentSharedDirect(data: {
    recipientId: string;
    sharerName: string;
    documentTitle: string;
    documentId: string;
    shareId: string;
  }): Promise<void> {
    await this.createNotification({
      userId: data.recipientId,
      type: "document_shared_direct",
      title: "Document Shared With You",
      message: `${data.sharerName} shared "${data.documentTitle}" with you`,
      data: {
        document_id: data.documentId,
        document_title: data.documentTitle,
        sharer_name: data.sharerName,
        share_id: data.shareId,
      },
    });
  },

  async notifyStorageWarning(data: {
    userId: string;
    storageUsed: number;
    storageLimit: number;
    percentage: number;
  }): Promise<void> {
    await this.createNotification({
      userId: data.userId,
      type: "storage_warning",
      title: "Storage Warning",
      message: `You're using ${
        data.percentage
      }% of your storage space (${Math.round(
        data.storageUsed / 1024 / 1024
      )}MB of ${Math.round(data.storageLimit / 1024 / 1024)}MB)`,
      data: {
        storage_used: data.storageUsed,
        storage_limit: data.storageLimit,
        percentage: data.percentage,
      },
    });
  },

  async notifySystemAnnouncement(data: {
    userIds: string[];
    title: string;
    message: string;
    data?: Record<string, any>;
  }): Promise<void> {
    const notifications = data.userIds.map((userId) => ({
      userId,
      type: "system_announcement" as NotificationType,
      title: data.title,
      message: data.message,
      data: data.data || {},
    }));

    // Create notifications in batch
    for (const notification of notifications) {
      await this.createNotification(notification);
    }
  },
};
