@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile Action Modal Animations */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Floating Action Button Animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fab-slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

.animate-fab-slide-up {
  animation: fab-slide-up 0.3s ease-out;
}

/* Touch-friendly improvements */
.touch-manipulation {
  touch-action: manipulation;
}

/* Enhanced folder icons with better shadows */
.folder-shadow {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Improved context menu positioning */
.context-menu-backdrop {
  backdrop-filter: blur(2px);
}

@layer base {
  * {
    @apply transition-theme duration-theme;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-dark-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Ensure smooth transitions for theme changes */
  html {
    @apply transition-theme duration-theme;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-theme duration-theme focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
    @apply dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:ring-blue-400 dark:focus:ring-offset-dark-800;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-lg transition-theme duration-theme focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
    @apply dark:bg-dark-700 dark:hover:bg-dark-600 dark:text-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-dark-800;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-theme duration-theme;
    @apply dark:bg-dark-800 dark:border-dark-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-blue-400 dark:focus:border-blue-400;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 transition-theme duration-theme;
    @apply dark:bg-dark-800 dark:border-dark-700 dark:shadow-lg;
  }

  /* Additional dark mode component classes */
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-theme duration-theme focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
    @apply dark:bg-red-600 dark:hover:bg-red-500 dark:focus:ring-red-400 dark:focus:ring-offset-dark-800;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-theme duration-theme focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
    @apply dark:bg-green-600 dark:hover:bg-green-500 dark:focus:ring-green-400 dark:focus:ring-offset-dark-800;
  }

  .text-muted {
    @apply text-gray-600 dark:text-gray-400;
  }

  .border-muted {
    @apply border-gray-200 dark:border-dark-700;
  }

  .bg-muted {
    @apply bg-gray-50 dark:bg-dark-800;
  }

  /* Additional dark mode utility classes */
  .checkbox-field {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-theme duration-theme;
    @apply dark:border-dark-600 dark:bg-dark-700 dark:text-primary-400 dark:focus:ring-primary-400;
  }

  .info-box {
    @apply bg-blue-50 border border-blue-200 rounded-lg p-4 transition-theme duration-theme;
    @apply dark:bg-blue-900/20 dark:border-blue-800;
  }

  .info-text {
    @apply text-blue-800 dark:text-blue-300 transition-theme duration-theme;
  }

  .success-box {
    @apply bg-green-50 border border-green-200 rounded-lg p-4 transition-theme duration-theme;
    @apply dark:bg-green-900/20 dark:border-green-800;
  }

  .success-text {
    @apply text-green-800 dark:text-green-300 transition-theme duration-theme;
  }

  .warning-box {
    @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4 transition-theme duration-theme;
    @apply dark:bg-yellow-900/20 dark:border-yellow-800;
  }

  .warning-text {
    @apply text-yellow-800 dark:text-yellow-300 transition-theme duration-theme;
  }

  .error-box {
    @apply bg-red-50 border border-red-200 rounded-lg p-4 transition-theme duration-theme;
    @apply dark:bg-red-900/20 dark:border-red-800;
  }

  .error-text {
    @apply text-red-800 dark:text-red-300 transition-theme duration-theme;
  }
}
