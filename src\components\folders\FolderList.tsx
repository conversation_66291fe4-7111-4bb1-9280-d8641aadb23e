import { useState } from "react";
import {
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import type { FolderWithChildren } from "../../lib/folderService";
import {
  useDragAndDrop,
  type DragItem,
  type DropTarget,
} from "../../hooks/useDragAndDrop";
import { formatDateTime } from "../../lib/utils";
import {
  ContextMenu,
  useContextMenu,
  createFolderContextMenuItems,
} from "../ui/ContextMenu";
import { FolderOperationsModal } from "./FolderOperationsModal";
import { FolderShareModal } from "./FolderShareModal";
import {
  ThreeDotMenu,
  useIsMobile,
  useTouchHandlers,
} from "../ui/ThreeDotMenu";

interface FolderListProps {
  folders: FolderWithChildren[];
  onFolderClick: (folderId: string) => void;
  onFolderEdit?: (folder: FolderWithChildren) => void;
  onFolderDelete?: (folder: FolderWithChildren) => void;
  onDrop?: (dragItem: DragItem, dropTarget: DropTarget) => void;
  onRefresh?: () => void;
  viewMode?: "grid" | "list";
  showActions?: boolean;
  enableDragAndDrop?: boolean;
  className?: string;
}

export function FolderList({
  folders,
  onFolderClick,
  onFolderEdit,
  onFolderDelete,
  onDrop,
  onRefresh,
  viewMode = "list",
  showActions = true,
  enableDragAndDrop = false,
  className = "",
}: FolderListProps) {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [contextMenuItems, setContextMenuItems] = useState<any[]>([]);
  const [operationsModal, setOperationsModal] = useState<{
    isOpen: boolean;
    operation: "copy" | "move" | "create_subfolder" | null;
    folder: FolderWithChildren | null;
  }>({
    isOpen: false,
    operation: null,
    folder: null,
  });
  const [shareModal, setShareModal] = useState<{
    isOpen: boolean;
    folder: FolderWithChildren | null;
  }>({
    isOpen: false,
    folder: null,
  });
  const contextMenu = useContextMenu();
  const isMobile = useIsMobile();
  const touchHandlers = useTouchHandlers();

  // Drag and drop functionality
  const { createDropHandlers, isDragOverTarget } = useDragAndDrop();

  const handleDropdownToggle = (folderId: string) => {
    setActiveDropdown(activeDropdown === folderId ? null : folderId);
  };

  const handleEdit = (folder: FolderWithChildren, e: React.MouseEvent) => {
    e.stopPropagation();
    setActiveDropdown(null);
    onFolderEdit?.(folder);
  };

  const handleDelete = (folder: FolderWithChildren, e: React.MouseEvent) => {
    e.stopPropagation();
    setActiveDropdown(null);
    onFolderDelete?.(folder);
  };

  if (folders.length === 0) {
    return (
      <div
        className={`text-center py-8 text-gray-500 dark:text-gray-400 transition-theme duration-theme ${className}`}
      >
        <div className="text-6xl mb-4">📁</div>
        <p>No folders found</p>
        <p className="text-sm mt-1">
          Create your first folder to organize your documents
        </p>
      </div>
    );
  }

  if (viewMode === "grid") {
    return (
      <div
        className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 ${className}`}
      >
        {folders.map((folder) => {
          const dropTarget: DropTarget = {
            type: "folder",
            id: folder.id,
            data: folder,
          };
          const dropHandlers =
            enableDragAndDrop && onDrop
              ? createDropHandlers(dropTarget, onDrop)
              : {};
          const isDropTarget =
            enableDragAndDrop && isDragOverTarget(dropTarget);

          return (
            <div
              key={folder.id}
              className="relative group cursor-pointer"
              onClick={() => onFolderClick(folder.id)}
            >
              <div
                className={`card p-4 hover:shadow-md transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 ${
                  isDropTarget
                    ? "bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-300 dark:border-blue-500"
                    : ""
                }`}
                {...dropHandlers}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="text-3xl">📁</div>
                  {showActions && (
                    <div className="relative">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDropdownToggle(folder.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-dark-600 transition-all duration-200"
                      >
                        <EllipsisVerticalIcon className="h-4 w-4 text-gray-500 dark:text-gray-400 transition-theme duration-theme" />
                      </button>

                      {activeDropdown === folder.id && (
                        <div className="absolute right-0 top-full mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                          <div className="py-1">
                            {onFolderEdit && (
                              <button
                                onClick={(e) => handleEdit(folder, e)}
                                className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <PencilIcon className="h-4 w-4 mr-2" />
                                Rename
                              </button>
                            )}
                            {onFolderDelete && (
                              <button
                                onClick={(e) => handleDelete(folder, e)}
                                className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Delete
                              </button>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-1">
                  <h4
                    className="font-medium text-gray-900 truncate"
                    title={folder.name}
                  >
                    {folder.name}
                  </h4>
                  <p className="text-xs text-gray-500">
                    {folder.document_count || 0} documents
                  </p>
                  <p className="text-xs text-gray-400">
                    {formatDateTime(folder.created_at)}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {folders.map((folder) => {
        const dropTarget: DropTarget = {
          type: "folder",
          id: folder.id,
          data: folder,
        };
        const dropHandlers =
          enableDragAndDrop && onDrop
            ? createDropHandlers(dropTarget, onDrop)
            : {};
        const isDropTarget = enableDragAndDrop && isDragOverTarget(dropTarget);

        const handleContextMenu = (e: React.MouseEvent) => {
          e.preventDefault();
          e.stopPropagation();

          const items = createFolderContextMenuItems(folder, {
            onOpen: () => onFolderClick(folder.id),
            onRename: onFolderEdit ? () => onFolderEdit(folder) : undefined,
            onDelete: onFolderDelete ? () => onFolderDelete(folder) : undefined,
            onCreateSubfolder: () => {
              setOperationsModal({
                isOpen: true,
                operation: "create_subfolder",
                folder: folder,
              });
            },
            onCopy: () => {
              setOperationsModal({
                isOpen: true,
                operation: "copy",
                folder: folder,
              });
            },
            onMove: () => {
              setOperationsModal({
                isOpen: true,
                operation: "move",
                folder: folder,
              });
            },
            onShare: () => {
              setShareModal({
                isOpen: true,
                folder: folder,
              });
            },
          });

          setContextMenuItems(items);
          contextMenu.openContextMenu(e);
        };

        return (
          <div
            key={folder.id}
            className="relative group cursor-pointer"
            onClick={() => onFolderClick(folder.id)}
            onContextMenu={isMobile ? undefined : handleContextMenu}
            {...(isMobile ? touchHandlers.touchHandlers : {})}
          >
            <div
              className={`card p-6 hover:shadow-lg transition-all duration-200 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 border-l-4 border-l-transparent hover:border-l-blue-400 dark:hover:border-l-blue-500 ${
                isDropTarget
                  ? "bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-300 dark:border-blue-500 shadow-lg border-l-blue-500"
                  : ""
              }`}
              {...dropHandlers}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className="text-3xl flex-shrink-0">📁</div>
                  <div className="flex-1 min-w-0">
                    <h4
                      className="font-medium text-gray-900 dark:text-gray-100 truncate transition-theme duration-theme"
                      title={folder.name}
                    >
                      {folder.name}
                    </h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <span>{folder.document_count || 0} documents</span>
                      <span>{formatDateTime(folder.created_at)}</span>
                    </div>
                    {folder.description && (
                      <p
                        className="text-sm text-gray-600 mt-1 truncate"
                        title={folder.description}
                      >
                        {folder.description}
                      </p>
                    )}
                  </div>
                </div>

                {showActions && (
                  <div className="relative flex-shrink-0">
                    {isMobile ? (
                      <ThreeDotMenu
                        title={folder.name}
                        items={createFolderContextMenuItems(folder, {
                          onOpen: () => onFolderClick(folder.id),
                          onRename: onFolderEdit
                            ? () => onFolderEdit(folder)
                            : undefined,
                          onDelete: onFolderDelete
                            ? () => onFolderDelete(folder)
                            : undefined,
                          onCreateSubfolder: () => {
                            setOperationsModal({
                              isOpen: true,
                              operation: "create_subfolder",
                              folder: folder,
                            });
                          },
                          onCopy: () => {
                            setOperationsModal({
                              isOpen: true,
                              operation: "copy",
                              folder: folder,
                            });
                          },
                          onMove: () => {
                            setOperationsModal({
                              isOpen: true,
                              operation: "move",
                              folder: folder,
                            });
                          },
                          onShare: () => {
                            setShareModal({
                              isOpen: true,
                              folder: folder,
                            });
                          },
                        })}
                        className="opacity-100"
                        size="sm"
                      />
                    ) : (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDropdownToggle(folder.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 p-2 rounded-full hover:bg-gray-200 transition-opacity duration-200"
                      >
                        <EllipsisVerticalIcon className="h-5 w-5 text-gray-500" />
                      </button>
                    )}

                    {activeDropdown === folder.id && (
                      <div className="absolute right-0 top-full mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                        <div className="py-1">
                          {onFolderEdit && (
                            <button
                              onClick={(e) => handleEdit(folder, e)}
                              className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <PencilIcon className="h-4 w-4 mr-2" />
                              Rename
                            </button>
                          )}
                          {onFolderDelete && (
                            <button
                              onClick={(e) => handleDelete(folder, e)}
                              className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                              <TrashIcon className="h-4 w-4 mr-2" />
                              Delete
                            </button>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}

      {/* Context Menu */}
      <ContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        items={contextMenuItems}
        onClose={contextMenu.closeContextMenu}
      />

      {/* Folder Operations Modal */}
      <FolderOperationsModal
        isOpen={operationsModal.isOpen}
        onClose={() =>
          setOperationsModal({ isOpen: false, operation: null, folder: null })
        }
        operation={operationsModal.operation}
        sourceFolder={operationsModal.folder}
        onSuccess={() => {
          // Refresh the folder list or trigger parent refresh
          onRefresh?.();
        }}
      />

      {/* Folder Share Modal */}
      <FolderShareModal
        isOpen={shareModal.isOpen}
        onClose={() => setShareModal({ isOpen: false, folder: null })}
        folder={shareModal.folder}
        onSuccess={() => {
          // Handle successful sharing
        }}
      />
    </div>
  );
}
